import { useEffect } from "react";
import Router from "./router";
import { useNavigate, useLocation } from "react-router-dom";
import { jwtDecode } from "jwt-decode";
import { useToast } from "@chakra-ui/react";
import { useState } from "react";

function App() {
  const [show , setShow] = useState(false);
  const toast = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const token = sessionStorage.getItem("admintoken");
  const publicPaths = ["/login", "/forgot-password", "/register", "/verification-pending"];

  useEffect(() => {
    const checkTokenValidity = () => {
      if (!token) {
        if (!publicPaths.includes(location.pathname)) {
          setShow(true);
          navigate("/login");
        } else {
          setShow(true);
        }
        return;
      } else {
        try {
          const decodedToken = jwtDecode(token.split(" ")[1]);
          const expirationTime = decodedToken.exp * 1000;
          const currentTime = Date.now();
          
          if (expirationTime < currentTime) {
            setShow(false);
            toast({
              title: "Session has expired, please login again",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
            sessionStorage.removeItem("admintoken");
            navigate("/verification-pending");
            return;
          }
          
          // Check if academy is authorized
          if (decodedToken?.academyId?.authStatus !== "authorized") {
            setShow(false);
            // If user is trying to access protected routes, redirect to verification-pending
            if (!publicPaths.includes(location.pathname)) {
              navigate("/verification-pending");
              return;
            }
          }
          setShow(true);
        } catch (error) {
          console.error("Error decoding token:", error);
          sessionStorage.removeItem("admintoken");
          navigate("/login");
          setShow(true);
        }
      }
    };

    checkTokenValidity();
  }, [navigate, token, location]);

  return (
    <>
      {show && <Router auth={token} />}
    </>
  );
}

export default App;
