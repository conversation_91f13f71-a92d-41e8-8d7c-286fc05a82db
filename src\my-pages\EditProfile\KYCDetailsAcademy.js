import React from "react";
import {
  Box,
  Card,
  CardBody,
  FormLabel,
  Heading,
  Image,
  ScaleFade,
  useColorModeValue,
  VStack,
  HStack,
  Badge,
  Text,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Flex,
  Icon,
} from "@chakra-ui/react";
import { LuBadgeCheck } from "react-icons/lu";

const KYCDetailsAcademy = ({ academyData }) => {
  // Color mode values for better theming - Greyish theme to match BasicDetailsAcademy
  const cardBg = useColorModeValue("gray.50", "gray.800");
  const readOnlyBg = useColorModeValue("gray.100", "gray.700");
  const borderColor = useColorModeValue("gray.300", "gray.600");
  const textColor = useColorModeValue("gray.800", "gray.200");
  const labelColor = useColorModeValue("gray.700", "gray.300");

  return (
    <ScaleFade initialScale={0.9} in={true}>
      <VStack spacing={{ base: 4, md: 6 }} align="stretch">
        {/* Header Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <Flex 
              direction={{ base: "column", sm: "row" }}
              justifyContent="space-between" 
              alignItems={{ base: "flex-start", sm: "center" }}
              gap={{ base: 4, sm: 0 }}
            >
              <HStack spacing={3}>
                <Heading size={{ base: "sm", md: "md" }} color="gray.600" fontWeight="bold">
                  KYC Details
                </Heading>
              </HStack>
            </Flex>
          </CardBody>
        </Card>

        {/* GST Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={6}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                GST Registration
              </Heading>
            </HStack>
            
            <VStack spacing={4} align="stretch">
              <Box>
                <FormLabel color={labelColor}>GST Registration Status</FormLabel>
                <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                  <HStack spacing={3}>
                    <Icon 
                      as={LuBadgeCheck} 
                      color={academyData?.gstNumber ? "green.500" : "gray.400"} 
                      boxSize={5}
                    />
                    <Text mb={0}>
                      {academyData?.gstNumber ? "GST Registered" : "Not GST Registered"}
                    </Text>
                    {academyData?.gstNumber && (
                      <Badge colorScheme="green" variant="subtle">
                        Active
                      </Badge>
                    )}
                  </HStack>
                </Box>
              </Box>

              {academyData?.gstNumber && (
                <Box>
                  <FormLabel color={labelColor}>GST Number</FormLabel>
                  <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                    <Text>{academyData.gstNumber}</Text>
                  </Box>
                </Box>
              )}
            </VStack>
          </CardBody>
        </Card>

        {/* Bank Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={6}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                Bank Details
              </Heading>
            </HStack>
            
            <VStack spacing={4} align="stretch">
              <Box>
                <FormLabel color={labelColor}>Account Holder Name</FormLabel>
                <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                  <Text>{academyData?.bankDetails?.accountHolderName}</Text>
                </Box>
              </Box>

              <Box>
                <FormLabel color={labelColor}>Account Number</FormLabel>
                <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                  <Text>{academyData?.bankDetails?.accountNumber}</Text>
                </Box>
              </Box>

              <Box>
                <FormLabel color={labelColor}>IFSC Code</FormLabel>
                <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                  <Text>{academyData?.bankDetails?.ifsc}</Text>
                </Box>
              </Box>
            </VStack>
          </CardBody>
        </Card>

        {/* PAN Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={6}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                PAN Details
              </Heading>
            </HStack>
            
            <VStack spacing={4} align="stretch">
              <Box>
                <FormLabel color={labelColor}>PAN Number</FormLabel>
                <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                  <Text>{academyData?.panNumber}</Text>
                </Box>
              </Box>

              <Box>
                <FormLabel color={labelColor}>PAN Card Images</FormLabel>
                <HStack spacing={4} align="start">
                  {academyData?.panImage?.map((image, index) => (
                    <Box key={index} p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor} flex="1">
                      <Text mb={2} fontSize="sm" fontWeight="medium" color={labelColor}>
                        {index === 0 ? 'Front Side' : 'Back Side'}
                      </Text>
                      <Image
                        src={image}
                        alt={`PAN Card ${index === 0 ? 'Front' : 'Back'}`}
                        maxW="100%"
                        maxH="200px"
                        objectFit="cover"
                        borderRadius="lg"
                      />
                    </Box>
                  ))}
                </HStack>
              </Box>
            </VStack>
          </CardBody>
        </Card>

        {/* Aadhaar Details Section */}
        <Card 
          bg={cardBg}
          shadow="xl"
          borderRadius="lg"
          border="1px"
          borderColor={borderColor}
          overflow="hidden"
          transition="all 0.3s ease"
          _hover={{
            shadow: "2xl",
            transform: "translateY(-2px)"
          }}
        >
          <CardBody p={{ base: 4, md: 6 }}>
            <HStack spacing={3} mb={6}>
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                Aadhaar Details
              </Heading>
            </HStack>
            
            <VStack spacing={4} align="stretch">
              <Box>
                <FormLabel color={labelColor}>Aadhaar Number</FormLabel>
                <Box p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
                  <Text>{academyData?.aadhaarNumber}</Text>
                </Box>
              </Box>

              <Box>
                <FormLabel color={labelColor}>Aadhaar Card Images</FormLabel>
                <HStack spacing={4} align="start">
                  {academyData?.aadhaarImage?.map((image, index) => (
                    <Box key={index} p={3} bg={readOnlyBg} borderRadius="lg" borderWidth="1px" borderColor={borderColor} flex="1">
                      <Text mb={2} fontSize="sm" fontWeight="medium" color={labelColor}>
                        {index === 0 ? 'Front Side' : 'Back Side'}
                      </Text>
                      <Image
                        src={image}
                        alt={`Aadhaar Card ${index === 0 ? 'Front' : 'Back'}`}
                        maxW="100%"
                        maxH="200px"
                        objectFit="cover"
                        borderRadius="lg"
                      />
                    </Box>
                  ))}
                </HStack>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </ScaleFade>
  );
};

export default KYCDetailsAcademy; 