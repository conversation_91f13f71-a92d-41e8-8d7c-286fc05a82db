import React, { useEffect, useState } from "react";
import {
  Avatar,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  useToast,
  FormErrorMessage,
  Box,
  ButtonGroup,
  Fade,
  ScaleFade,
  useColorModeValue,
  VStack,
  HStack,
  Tooltip,
  Badge,
} from "@chakra-ui/react";
import { LuCheck, LuPencilLine, LuX, LuCamera } from "react-icons/lu";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { setCookie } from "../../utilities/auth";

const BasicDetailsAcademy = ({ academyData }) => {
  const [profileImagePreview, setProfileImagePreview] = useState("");
  const [isSubmitBtnLoading, setIsSubmitBtnLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  // Color mode values for better theming - Greyish theme to match coach creation
  const cardBg = useColorModeValue("gray.50", "gray.800");
  const readOnlyBg = useColorModeValue("gray.100", "gray.700");
  const borderColor = useColorModeValue("gray.300", "gray.600");
  const textColor = useColorModeValue("gray.800", "gray.200");
  const labelColor = useColorModeValue("gray.700", "gray.300");

  const handleProfileImageChange = async (e) => {
    try {
      const file = e.currentTarget.files[0];
      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB",
          status: "warning",
          duration: 5000,
          position: "top",
          isClosable: true,
        });
        return;
      }
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;

      if (url) {
        toast({
          title: "Profile image uploaded",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      } else {
        toast({
          title: "Something went wrong while uploading profile image",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      }
      formik.setFieldValue("profileImg", url);
      setProfileImagePreview(url);
    } catch (error) {
      console.log(error);
      toast({
        title: "Something went wrong please try again later",
        status: "error",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const validationSchema = Yup.object().shape({
    academyName: Yup.string()
      .required("Academy name is required")
      .min(3, "Academy name must be at least 3 characters")
      .max(50, "Academy name must be less than or equal to 50 characters"),
    phoneNumber: Yup.string()
      .required("Phone number is required")
      .matches(/^\d{10}$/, "Phone number must be exactly 10 digits"),
  });

  const formik = useFormik({
    initialValues: {
      academyName: academyData?.name || "",
      email: academyData?.email || "",
      phoneNumber: academyData?.mobile || "",
      profileImg: academyData?.profileImage,
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      setIsSubmitBtnLoading(true);
      try {
        const payload = {
          name: values.academyName,
          profileImage: values.profileImg,
          mobile: values.phoneNumber,
        };
        const response = await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/academy/${academyData?._id}`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.data.status === "success") {
          toast({
            title: "Academy updated successfully",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsEditMode(false);
          setCookie("academyName", response.data.data.name);
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else {
          toast({
            title: response.data.message || "Something went wrong",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      } catch (error) {
        console.log(error);
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } finally {
        setIsSubmitBtnLoading(false);
      }
    },
  });

  useEffect(() => {
    if (academyData?.profileImage) {
      setProfileImagePreview(academyData.profileImage);
    }
  }, [academyData]);

  const handleEdit = () => {
    setIsEditMode(true);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  const handleCancel = () => {
    formik.resetForm();
    setIsEditMode(false);
  };

  return (
    <ScaleFade initialScale={0.9} in={true}>
      <Card
        bg={cardBg}
        shadow="xl"
        borderRadius="lg"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        transition="all 0.3s ease"
        _hover={{
          shadow: "2xl",
          transform: "translateY(-2px)",
        }}
      >
        <CardBody p={{ base: 4, md: 8 }}>
          <Flex
            justifyContent="space-between"
            alignItems="center"
            mb={{ base: 4, md: 6 }}
            direction={{ base: "column", md: "row" }}
            gap={{ base: 3, md: 0 }}
          >
            <HStack spacing={3}>
              <Heading
                size={{ base: "sm", md: "md" }}
                color="gray.600"
                fontWeight="bold"
              >
                Basic Details
              </Heading>
            </HStack>
            <Fade in={true}>
              {!isEditMode ? (
                <Tooltip label="Edit profile information" placement="top">
                  <Button
                    colorScheme="gray"
                    variant="outline"
                    onClick={handleEdit}
                    leftIcon={<LuPencilLine />}
                    size={{ base: "sm", md: "md" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md",
                    }}
                  >
                    Edit
                  </Button>
                </Tooltip>
              ) : (
                <ButtonGroup spacing={{ base: 2, md: 3 }}>
                  <Button
                    colorScheme="blue"
                    onClick={handleSave}
                    isLoading={isSubmitBtnLoading}
                    leftIcon={<LuCheck />}
                    size={{ base: "sm", md: "md" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md",
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    colorScheme="red"
                    onClick={handleCancel}
                    leftIcon={<LuX />}
                    size={{ base: "sm", md: "md" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md",
                    }}
                  >
                    Cancel
                  </Button>
                </ButtonGroup>
              )}
            </Fade>
          </Flex>

          <Divider borderColor={borderColor} mb={{ base: 6, md: 8 }} />

          <VStack spacing={{ base: 6, md: 8 }} align="stretch">
            {/* Profile Image Section */}
            <FormControl>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <FormLabel
                  fontWeight="semibold"
                  color={labelColor}
                  mb={0}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  Profile Image
                </FormLabel>
              </HStack>
              <Flex direction="column" align="center" gap={{ base: 2, md: 3 }}>
                <Box position="relative">
                  <Avatar
                    size={{ base: "xl", md: "2xl" }}
                    src={profileImagePreview}
                    border="4px solid"
                    borderColor="gray.200"
                    shadow="lg"
                    transition="all 0.3s ease"
                    _hover={{
                      borderColor: "gray.400",
                      shadow: "xl",
                    }}
                  />
                  {isEditMode && (
                    <Badge
                      position="absolute"
                      top={-2}
                      right={-2}
                      colorScheme="gray"
                      borderRadius="full"
                      p={1}
                    >
                      Edit
                    </Badge>
                  )}
                </Box>
                {isEditMode && (
                  <Fade in={isEditMode}>
                    <VStack spacing={3}>
                      <Input
                        type="file"
                        id="profileImageInput"
                        onChange={handleProfileImageChange}
                        style={{ display: "none" }}
                        accept="image/*"
                      />
                      <Button
                        onClick={() =>
                          document.getElementById("profileImageInput").click()
                        }
                        colorScheme="gray"
                        size={{ base: "sm", md: "md" }}
                        variant="outline"
                        borderRadius="full"
                        leftIcon={<LuCamera />}
                        transition="all 0.2s"
                        _hover={{
                          transform: "scale(1.05)",
                          shadow: "md",
                        }}
                      >
                        Upload New Image
                      </Button>
                    </VStack>
                  </Fade>
                )}
              </Flex>
            </FormControl>

            {/* Academy Name Section */}
            <FormControl
              isInvalid={
                formik.errors.academyName && formik.touched.academyName
              }
            >
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <FormLabel
                  fontWeight="semibold"
                  color={labelColor}
                  mb={0}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  Academy Name
                </FormLabel>
              </HStack>
              <Fade in={true}>
                {isEditMode ? (
                  <Input
                    type="text"
                    name="academyName"
                    value={formik.values.academyName}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter academy name"
                    size={{ base: "sm", md: "md" }}
                    borderRadius="lg"
                    border="1px"
                    borderColor={borderColor}
                    transition="all 0.2s"
                    _hover={{
                      borderColor: "gray.400",
                    }}
                    _focus={{
                      borderColor: "gray.500",
                      shadow: "0 0 0 1px var(--chakra-colors-gray-500)",
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 2, md: 3 }}
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "32px", md: "36px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm",
                    }}
                  >
                    {formik.values.academyName || "No academy name provided"}
                  </Box>
                )}
              </Fade>
              <FormErrorMessage>{formik.errors.academyName}</FormErrorMessage>
            </FormControl>

            {/* Email Section */}
            <FormControl>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <FormLabel
                  fontWeight="semibold"
                  color={labelColor}
                  mb={0}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  Email Address
                </FormLabel>
              </HStack>
              <Fade in={true}>
                <Box
                  p={{ base: 2, md: 3 }}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "32px", md: "36px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm",
                  }}
                >
                  {formik.values.email || "No email provided"}
                </Box>
              </Fade>
            </FormControl>

            {/* Password Section */}
            <FormControl>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <FormLabel
                  fontWeight="semibold"
                  color={labelColor}
                  mb={0}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  Password
                </FormLabel>
              </HStack>
              <Fade in={true}>
                <Box
                  p={{ base: 2, md: 3 }}
                  borderWidth="1px"
                  borderRadius="lg"
                  bg={readOnlyBg}
                  color={textColor}
                  minH={{ base: "32px", md: "36px" }}
                  display="flex"
                  alignItems="center"
                  borderColor={borderColor}
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="medium"
                  transition="all 0.2s"
                  _hover={{
                    shadow: "sm",
                  }}
                >
                  {"••••••••••••"}
                </Box>
              </Fade>
            </FormControl>

            {/* Phone Number Section */}
            <FormControl
              isInvalid={
                formik.errors.phoneNumber && formik.touched.phoneNumber
              }
            >
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                <FormLabel
                  fontWeight="semibold"
                  color={labelColor}
                  mb={0}
                  fontSize={{ base: "sm", md: "md" }}
                >
                  Phone Number
                </FormLabel>
              </HStack>
              <Fade in={true}>
                {isEditMode ? (
                  <Input
                    type="text"
                    name="phoneNumber"
                    value={formik.values.phoneNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter phone number"
                    size={{ base: "sm", md: "md" }}
                    borderRadius="lg"
                    border="1px"
                    borderColor={borderColor}
                    transition="all 0.2s"
                    _hover={{ borderColor: "gray.400" }}
                    _focus={{
                      borderColor: "gray.500",
                      shadow: "0 0 0 1px var(--chakra-colors-gray-500)",
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 2, md: 3 }}
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "32px", md: "36px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{ shadow: "sm" }}
                  >
                    {formik.values.phoneNumber || "No phone number provided"}
                  </Box>
                )}
              </Fade>
              <FormErrorMessage>{formik.errors.phoneNumber}</FormErrorMessage>
            </FormControl>
          </VStack>
        </CardBody>
      </Card>
    </ScaleFade>
  );
};

export default BasicDetailsAcademy;
