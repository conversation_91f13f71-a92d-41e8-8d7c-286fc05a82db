import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Tooltip,
  Badge,
  useToast,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Button,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  Tag,
  Heading,
  Card,
  CardBody,
  ScaleFade,
  Fade,
  useColorModeValue,
  Skeleton,
  SkeletonText,
  Icon,
  VStack,
  HStack,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import { LuChevronRight } from "react-icons/lu";
import axios from "axios";
import { FaAngleDown } from "react-icons/fa";
import { useSelector, useDispatch } from "react-redux";
import { setAcademyProfileImage } from "../Auth/AuthSlice";
import BasicDetailsAcademy from "./BasicDetailsAcademy";
import ProfessionalDetailsAcademy from "./ProfessionalDetailsAcademy";
import KYCDetailsAcademy from "./KYCDetailsAcademy";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const EditProfile = () => {
  const [profileData, setProfileData] = useState({
    result: {},
    error: false,
    isLoading: false,
  });

  const [profileStatusChange, setProfileStatusChange] = useState({
    type: "",
    id: "",
  });

  const [renderMe, setRenderMe] = useState(0);
  const userData = useSelector((state) => state.user);
  const dispatch = useDispatch();

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const navigate = useNavigate();
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const academyId = sessionStorage.getItem("academyId");

  // Color mode values
  const bgColor = useColorModeValue("#f8fafc", "gray.900");
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const mutedTextColor = useColorModeValue("gray.500", "gray.400");

  useEffect(() => {
    setProfileData({ result: {}, error: false, isLoading: true });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy/${academyId}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setProfileData({ result: response.data.data, error: false, isLoading: false });
        if (response.data.data?.profileImage) {
          dispatch(setAcademyProfileImage(response.data.data.profileImage));
        }
      })
      .catch((error) => {
        console.log(error);
        setProfileData({ result: {}, error: true, isLoading: false });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  }, [academyId, renderMe, dispatch]);

  return (
    <Box bgColor={cardBg} minH="100vh" px={{ base: 0, md: 0 }}>
      <Layout title="Edit Profile">
        <ScaleFade initialScale={0.9} in={true}>
          
              <Flex 
                direction={{ base: "column", md: "row" }}
                justifyContent={"space-between"} 
                alignItems={{ base: "flex-start", md: "center" }}
                gap={{ base: 4, md: 0 }}
                mb={3}
              >
                <Flex justifyContent={"center"} alignItems={"center"} flexWrap="wrap">
                  <Tooltip label="Back">
                    <Button
                      variant="ghost"
                      size={{ base: "xs", md: "sm" }}
                      mr={{ base: 2, md: 3 }}
                      leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
                      onClick={() => navigate(-1)}
                      _hover={{ bg: "gray.100" }}
                      className="p-0"
                    >
                      <Box display={{ base: "none", sm: "block" }}></Box>
                    </Button>
                  </Tooltip>
                  <Breadcrumb 
                    fontWeight="medium" 
                    fontSize={{ base: "xs", md: "sm" }}
                    separator={<Icon as={LuChevronRight} color={mutedTextColor} />}
                  >

                    <BreadcrumbItem isCurrentPage>
                      <BreadcrumbLink 
                        href="#" 
                        color={textColor}
                        fontWeight="semibold"
                      >
                        <Box display={{ base: "none", md: "block" }}>
                          Update Profile - 
                          {profileData?.result?.name
                            ? profileData.result.name
                            : academyId}
                        </Box>
                        <Box display={{ base: "block", md: "none" }} fontWeight="medium" fontSize="sm" pb={0.5}>
                          Edit Profile
                        </Box>
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </Breadcrumb>
                </Flex>
                
                {userData?.accessScopes?.profile?.includes("read") &&
                  !userData?.accessScopes?.profile?.includes("write") && (
                    <Fade in={true}>
                      <Badge
                        colorScheme={
                          profileData?.result?.status === "active" ? "green" : "red"
                        }
                        variant="solid"
                        fontSize="0.9em"
                        px={3}
                        py={1}
                        borderRadius="full"
                      >
                        {profileData?.result?.status?.toUpperCase()}
                      </Badge>
                    </Fade>
                  )}

                {userData?.accessScopes?.profile?.includes("write") && (
                  <Fade in={true}>
                    <Menu>
                      <Tooltip
                        label={
                          !profileData?.result?.kycDocuments?.documentImg?.length &&
                          "KYC details not available"
                        }
                      >
                        <MenuButton
                          as={Button}
                          size={{ base: "xs", md: "sm" }}
                          variant={"outline"}
                          isDisabled={
                            !profileData?.result?.kycDocuments?.documentImg?.length
                          }
                          colorScheme={
                            profileData?.result?.status === "active" ? "green" : "red"
                          }
                          rightIcon={<FaAngleDown />}
                          _hover={{ transform: "translateY(-1px)", shadow: "md" }}
                          transition="all 0.2s"
                          fontSize={{ base: "xs", md: "sm" }}
                        >
                          {profileData?.result?.status?.toUpperCase()}
                        </MenuButton>
                      </Tooltip>
                      <MenuList>
                        <MenuItem
                          isDisabled={profileData?.result?.status === "active"}
                          onClick={() => {
                            setProfileStatusChange({
                              type: "active",
                              id: profileData?.result?._id,
                            });
                            onOpen3();
                          }}
                        >
                          Active
                        </MenuItem>
                        <MenuItem
                          isDisabled={profileData?.result?.status !== "active"}
                          onClick={() => {
                            setProfileStatusChange({
                              type: "inactive",
                              id: profileData?.result?._id,
                            });
                            onOpen3();
                          }}
                        >
                          Inactive
                        </MenuItem>
                      </MenuList>
                    </Menu>
                  </Fade>
                )}
              </Flex>
            
        </ScaleFade>
        {profileData.isLoading && !profileData.error ? (
          <Card bg={cardBg} shadow="sm" border="1px" borderColor={borderColor}>
            <CardBody p={{ base: 4, md: 6 }}>
              <VStack spacing={6} align="stretch">
                <HStack 
                  spacing={4} 
                  direction={{ base: "column", md: "row" }}
                  align={{ base: "stretch", md: "center" }}
                  w="full"
                >
                  <Skeleton height="40px" width={{ base: "100%", md: "120px" }} borderRadius="md" />
                  <Skeleton height="40px" width={{ base: "100%", md: "140px" }} borderRadius="md" />
                  <Skeleton height="40px" width={{ base: "100%", md: "120px" }} borderRadius="md" />
                </HStack>
                <VStack spacing={4} align="stretch">
                  <Skeleton height="20px" width={{ base: "100%", md: "60%" }} />
                  <SkeletonText mt="4" noOfLines={4} spacing="4" skeletonHeight="2" />
                  <Skeleton height="40px" width="100%" />
                  <SkeletonText mt="4" noOfLines={3} spacing="4" skeletonHeight="2" />
                </VStack>
              </VStack>
            </CardBody>
          </Card>
        ) : (
          <Fade in={!profileData.isLoading}>
            <Card bg={cardBg} shadow="sm">
              <CardBody p={0}>
                <Tabs 
                  colorScheme="blue" 
                  size={{ base: "sm", md: "md" }}
                  variant="line"
                  w="full"
                  orientation={{ base: "horizontal", lg: "horizontal" }}
                  isLazy
                >
                  <TabList 
                    borderBottom="1px" 
                    borderColor={borderColor}
                    overflowX={{ base: "auto", md: "visible" }}
                    overflowY="hidden"
                    whiteSpace="nowrap"
                    sx={{
                      '&::-webkit-scrollbar': {
                        height: '4px'
                      },
                      '&::-webkit-scrollbar-track': {
                        background: 'transparent'
                      },
                      '&::-webkit-scrollbar-thumb': {
                        background: 'gray.300',
                        borderRadius: '2px'
                      }
                    }}
                  >
                    <Tab 
                      _selected={{ 
                        color: "blue.600", 
                        borderBottomColor: "blue.500",
                        borderBottomWidth: "3px",
                        borderBottomStyle: "solid"
                      }}
                      _hover={{ color: "blue.500" }}
                      transition="all 0.2s"
                      fontWeight="medium"
                      fontSize={{ base: "xs", md: "sm" }}
                      px={{ base: 3, md: 4 }}
                      minW={{ base: "120px", md: "auto" }}
                      borderBottom="3px solid transparent"
                    >
                      <Box display={{ base: "none", sm: "block" }}>Basic Details</Box>
                      <Box display={{ base: "block", sm: "none" }}>Basic</Box>
                    </Tab>
                    <Tab 
                      _selected={{ 
                        color: "blue.600", 
                        borderBottomColor: "blue.500",
                        borderBottomWidth: "3px",
                        borderBottomStyle: "solid"
                      }}
                      _hover={{ color: "blue.500" }}
                      transition="all 0.2s"
                      fontWeight="medium"
                      fontSize={{ base: "xs", md: "sm" }}
                      px={{ base: 3, md: 4 }}
                      minW={{ base: "140px", md: "auto" }}
                      borderBottom="3px solid transparent"
                    >
                      <Box display={{ base: "none", sm: "block" }}>Professional Details</Box>
                      <Box display={{ base: "block", sm: "none" }}>Professional</Box>
                    </Tab>
                    <Tab 
                      _selected={{ 
                        color: "blue.600", 
                        borderBottomColor: "blue.500",
                        borderBottomWidth: "3px",
                        borderBottomStyle: "solid"
                      }}
                      _hover={{ color: "blue.500" }}
                      transition="all 0.2s"
                      fontWeight="medium"
                      fontSize={{ base: "xs", md: "sm" }}
                      px={{ base: 3, md: 4 }}
                      minW={{ base: "100px", md: "auto" }}
                      borderBottom="3px solid transparent"
                    >
                      <Box display={{ base: "none", sm: "block" }}>KYC Details</Box>
                      <Box display={{ base: "block", sm: "none" }}>KYC</Box>
                    </Tab>
                  </TabList>

                  <TabPanels>
                    <TabPanel p={{ base: 1 }} py={{ base: 4, md: 6 }}>
                      <ScaleFade initialScale={0.95} in={true}>
                        <BasicDetailsAcademy academyData={profileData.result} />
                      </ScaleFade>
                    </TabPanel>
                    <TabPanel p={{ base: 1 }} py={{ base: 4, md: 6 }}>
                      <ScaleFade initialScale={0.95} in={true}>
                        <ProfessionalDetailsAcademy academyData={profileData.result} />
                      </ScaleFade>
                    </TabPanel>
                    <TabPanel p={{ base: 1 }} py={{ base: 4, md: 6 }}>
                      <ScaleFade initialScale={0.95} in={true}>
                        <KYCDetailsAcademy academyData={profileData.result} />
                      </ScaleFade>
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </CardBody>
            </Card>
          </Fade>
        )}
      </Layout>
    </Box>
  );
};

export default EditProfile; 