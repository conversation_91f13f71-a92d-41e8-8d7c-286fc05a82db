import { useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  Flex,
  <PERSON>ing,
  Text,
  Button,
  IconButton,
  Badge,
  SimpleGrid,
  useToast,
  Tooltip,
} from "@chakra-ui/react";
import { ChevronLeftIcon, ChevronRightIcon } from "@chakra-ui/icons";
import Calendar from "react-calendar";
import moment from "moment-timezone";
import "react-calendar/dist/Calendar.css";
import "../Dashboard/CalendarStyle.css";
import "./datePicker.css";

export default function CoachCalendar({ coachData }) {
  const toast = useToast();

  const [currentDate, setCurrentDate] = useState(new Date());
  const [allEvents, setAllEvents] = useState([]);

  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];

  const daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const formatDateToYYYYMMDD = (dateString) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = `0${date.getMonth() + 1}`.slice(-2);
    const day = `0${date.getDate()}`.slice(-2);
    return `${year}-${month}-${day}`;
  };

  const handleDay = (action) => {
    const newDate = new Date(currentDate);
    if (action === "prev") newDate.setDate(newDate.getDate() - 1);
    if (action === "next") newDate.setDate(newDate.getDate() + 1);
    if (action === "today") newDate.setTime(Date.now());
    setCurrentDate(newDate);
  };

  const googleEvents = async () => {
    try {
      // Check if coachData exists and has an _id before making the API call
      if (!coachData || !coachData._id) {
        console.log("Coach data not available yet");
        return;
      }
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      headers.append("Authorization", `Bearer ${token}`);

      const data = {
        coachId: coachData._id,
        startDate: moment(currentDate).startOf("day"),
        endDate: moment(currentDate).endOf("day"),
      };

      const requestOptions = {
        method: "POST",
        headers,
        body: JSON.stringify(data),
      };

      const response = await fetch(
        `${process.env.REACT_APP_BASE_URL}/api/calendar/eventList`,
        requestOptions
      );
      const result = await response.json();

      if (result && !result.error) {
        const todayEvents = result.data?.filter(
          (x) =>
            formatDateToYYYYMMDD(currentDate) ===
            formatDateToYYYYMMDD(new Date(x.start.dateTime))
        );
        setAllEvents(todayEvents || []);
      } else {
       console.error("Error fetching events:", result.error);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    googleEvents();
  }, [currentDate, coachData]);

  // Color mapping based on colorId
  const getEventColors = (colorId) => {
    const colorMap = {
      1: {
        bg: "red.200",
        border: "red.500",
        text: "black",
        hoverText: "black",
        bgSolid: "red.500",
      },
      2: {
        bg: "gray.200",
        border: "gray.500",
        text: "black",
        hoverText: "black",
        bgSolid: "gray.500",
      },
      3: {
        bg: "green.200",
        border: "green.500",
        text: "black",
        hoverText: "black",
        bgSolid: "green.500",
      },
      4: {
        bg: "yellow.200",
        border: "yellow.500",
        text: "black",
        hoverText: "black ",
        bgSolid: "yellow.500",
      },
      5: {
        bg: "blue.200",
        border: "blue.500",
        text: "black",
        hoverText: "black",
        bgSolid: "blue.500",
      },
      default: {
        bg: "blue.200",
        border: "blue.500",
        text: "black",
        hoverText: "black",
        bgSolid: "blue.500",
      },
    };
    return colorMap[colorId] || colorMap.default;
  };

  const positionedEvents = useMemo(() => {
    const sorted = [...allEvents].sort(
      (a, b) => new Date(a.start.dateTime) - new Date(b.start.dateTime)
    );
    const positioned = [];

    sorted.forEach((event) => {
      const startA = new Date(event.start.dateTime);
      const endA = new Date(event.end.dateTime);

      let colIndex = 0;
      let overlap = positioned.filter((e) => {
        const startB = new Date(e.start.dateTime);
        const endB = new Date(e.end.dateTime);
        return startA < endB && endA > startB;
      });

      while (overlap.find((e) => e.colIndex === colIndex)) {
        colIndex++;
      }

      positioned.push({
        ...event,
        colIndex,
        colTotal: Math.max(colIndex + 1, ...overlap.map((e) => e.colIndex + 1)),
      });
    });

    return positioned;
  }, [allEvents]);

  return (
    <Box h="100vh" display="flex" flexDirection="column" bg="gray.50">
      {/* Header */}
      <Box
        as="header"
        display="flex"
        flexDirection={{ base: "column", md: "row" }}
        alignItems="center"
        justifyContent={{ base: "center", md: "space-between" }}
        borderBottomWidth="1px"
        borderColor="gray.200"
        px={6}
        py={4}
        position="sticky"
        top={0}
        bg="white"
        boxShadow="sm"
        zIndex={11}
      >
        <Box>
          <Heading size="md" fontWeight="semibold">{`${currentDate.getDate()} ${
            months[currentDate.getMonth()]
          } ${currentDate.getFullYear()}`}</Heading>
          <Text color="gray.500" fontSize="sm">
            {daysOfWeek[currentDate.getDay()]}
          </Text>
          {coachData?.academyAvailability && (
            <Text color="blue.600" fontSize="sm" fontWeight="medium" mt={1} textAlign="center">
              Start Time :{coachData.academyAvailability.startTime} -  End Time{coachData.academyAvailability.endTime}
            </Text>
          )}
        </Box>

        <Box
          overflowX="auto"
          overflowY="hidden"
          mt={{ base: 4, md: 0 }}
          maxW="100%"
          css={{
            '&::-webkit-scrollbar': {
              height: '4px',
            },
            '&::-webkit-scrollbar-track': {
              background: 'transparent',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#CBD5E0',
              borderRadius: '2px',
            },
          }}
        >
          <Flex gap={2} minW="max-content">
            <Badge colorScheme="red" variant="solid">
              Breaks
            </Badge>
            <Badge colorScheme="gray" variant="solid">
              Courses
            </Badge>
            <Badge colorScheme="green" variant="solid">
              Bookings
            </Badge>
            <Badge colorScheme="yellow" variant="solid">
              Sessions
            </Badge>
            <Badge colorScheme="blue" variant="outline">
              Other Events
            </Badge>
          </Flex>
        </Box>

        <Flex mt={{ base: 4, md: 0 }}>
          <IconButton
            icon={<ChevronLeftIcon />}
            onClick={() => handleDay("prev")}
            size="sm"
            mr={2}
          />
          <Button onClick={() => handleDay("today")} size="sm" variant="ghost">
            Today
          </Button>
          <IconButton
            icon={<ChevronRightIcon />}
            onClick={() => handleDay("next")}
            size="sm"
            ml={2}
          />
        </Flex>
      </Box>

      {/* Show calendar at top on mobile */}
      <Box display={{ base: "flex", md: "none" }} justifyContent="center" px={4} py={2} bg="white" boxShadow="sm" w="100%" pb={6}>
        <Calendar
          onChange={(value) => setCurrentDate(value)}
          value={currentDate}
          prev2Label={null}
          next2Label={null}
          className="custom-calendar"
        />
      </Box>

      {/* Calendar Grid */}
      <Flex flex="1" overflow="hidden" position="relative">
        <Box flex="1" overflow="auto" bg="white">
          <Box position="relative" h="1440px">
            {/* Time Labels */}
            <Box
              position="absolute"
              left={0}
              top={0}
              bottom={0}
              w="70px"
              zIndex={1}
              bg="gray.50"
              borderRightWidth="2px"
              borderColor="gray.300"
            >
              {[...Array(24)].map((_, i) => (
                <Box
                  key={i}
                  h="60px"
                  position="relative"
                  borderBottomWidth="1px"
                  borderColor="gray.200"
                  display="flex"
                  alignItems="flex-start"
                  justifyContent="flex-end"
                  pr={2}
                  pt={1}
                >
                  <Text fontSize="xs" color="gray.600" fontWeight="medium">
                    {i === 0
                      ? "12AM"
                      : i < 12
                        ? `${i}AM`
                        : i === 12
                          ? "12PM"
                          : `${i - 12}PM`}
                  </Text>
                </Box>
              ))}
            </Box>

            {/* Events Grid */}
            <Box
              position="absolute"
              left="70px"
              right={0}
              top={0}
              bottom={0}
              pl={3}
            >
              {[...Array(24)].map((_, i) => (
                <Box key={`hour-${i}`}>
                  <Box
                    h="30px"
                    borderBottomWidth="1px"
                    borderColor="gray.100"
                    borderStyle="dashed"
                  />
                  <Box
                    h="30px"
                    borderBottomWidth="1px"
                    borderColor="gray.200"
                    borderStyle="solid"
                  />
                </Box>
              ))}

              {positionedEvents.map((event, index) => {
                const start = new Date(event.start.dateTime);
                const end = new Date(event.end.dateTime);

                const top = start.getHours() * 60 + start.getMinutes();
                const isShortEvent = (end - start) / (1000 * 60) < 30; // less than 30 minutes
                const height = Math.max(
                  (end - start) / (1000 * 60),
                  isShortEvent ? 45 : 30
                ); // increased height for short events

                const width = 100 / event.colTotal;
                const left = event.colIndex * width;

                const colors = getEventColors(event.colorId);

                const tooltipLabel = `${event.summary}\n${start
                  .toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                  })
                  .toLowerCase()} - ${end
                    .toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                      hour12: true,
                    })
                    .toLowerCase()}${event.description ? `\n${event.description}` : ""
                  }`;

                return (
                  <Tooltip
                    key={index}
                    label={tooltipLabel}
                    placement="top"
                    hasArrow
                    bg="gray.800"
                    color="white"
                    fontSize="sm"
                    whiteSpace="pre-line"
                  >
                    <Box
                      position="absolute"
                      top={`${top}px`}
                      height={`${height}px`}
                      left={`${left}%`}
                      width={`${width}%`}
                      px={1}
                      zIndex={2}
                    >
                      <Box
                        bg={colors.bg}
                        borderRadius="md"
                        p={isShortEvent ? 1 : 2}
                        h="full"
                        borderLeftWidth="4px"
                        borderColor={colors.border}
                        boxShadow="sm"
                        display="flex"
                        flexDirection="column"
                        justifyContent={isShortEvent ? "center" : "flex-start"}
                        cursor="pointer"
                        transition="all 0.2s"
                        _hover={{
                          bg: colors.bgSolid,
                          transform: "scale(1.02)",
                          boxShadow: "md",
                        }}
                        overflow="hidden"
                        role="group"
                      >
                        <Text
                          fontWeight="medium"
                          fontSize={isShortEvent ? "xs" : "sm"}
                          mb={isShortEvent ? 0 : 1}
                          color={colors.text}
                          isTruncated
                          _groupHover={{
                            color: colors.hoverText,
                          }}
                        >
                          {event.summary}
                        </Text>
                        {!isShortEvent && (
                          <Text
                            fontSize="xs"
                            color="gray.600"
                            _groupHover={{
                              color: "gray.200",
                            }}
                          >
                            {start
                              .toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: true,
                              })
                              .toLowerCase()}{" "}
                            -{" "}
                            {end
                              .toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: true,
                              })
                              .toLowerCase()}
                          </Text>
                        )}
                        {isShortEvent && (
                          <Text
                            fontSize="xs"
                            color="gray.600"
                            isTruncated
                            _groupHover={{
                              color: "gray.200",
                            }}
                          >
                            {start
                              .toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: true,
                              })
                              .toLowerCase()}
                            {" "}
                            -{" "}
                            {end
                              .toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                                hour12: true,
                              })
                              .toLowerCase()}
                          </Text>
                        )}
                      </Box>
                    </Box>
                  </Tooltip>
                );
              })}
            </Box>
          </Box>
        </Box>

        {/* Sidebar Calendar */}
        <Box
          display={{ base: "none", md: "block" }}
          w="96"
          borderLeftWidth="1px"
          borderColor="gray.200"
          p={6}
          bg="white"
          boxShadow="md"
        >
          <Calendar
            onChange={(value) => setCurrentDate(value)}
            value={currentDate}
            prev2Label={null}
            next2Label={null}
            className="custom-calendar"
          />
        </Box>
      </Flex>
    </Box>
  );
}
