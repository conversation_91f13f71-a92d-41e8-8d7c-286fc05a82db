

"use client";

import React, { useState, useCallback } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  useToast,
  Box,
  Flex,
  Heading,
  Text,
  Input,
  Textarea,
  Button,
  FormControl,
  FormLabel,
  FormErrorMessage,
  useMediaQuery,
  Stack,
  Icon,
  chakra,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import { MdLocationOn, MdEmail, MdPhone, MdLanguage } from "react-icons/md";
import Layout from "../../layout/default";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { useNavigate } from "react-router-dom";


const ContactSchema = Yup.object({
  firstName: Yup.string()
    .trim()
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name cannot exceed 50 characters")
    .matches(/^[a-zA-Z\s]+$/, "First name can only contain letters and spaces")
    .required("First name is required"),
  lastName: Yup.string()
    .trim()
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name cannot exceed 50 characters")
    .matches(/^[a-zA-Z\s]+$/, "Last name can only contain letters and spaces")
    .required("Last name is required"),
  email: Yup.string()
    .email("Please enter a valid email address")
    .required("Email is required"),
  mobile: Yup.string()
    .matches(/^[+]?[0-9]{10,11}$/, "Please enter a valid phone number (10 digits)")
    .required("Phone number is required"),
  message: Yup.string()
    .trim()
    .min(10, "Message must be at least 10 characters")
    .max(1000, "Message cannot exceed 1000 characters")
    .required("Message is required"),
});

function Contact() {
  // Hooks
  const toast = useToast();
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const navigate = useNavigate();

  // State management
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Utility function for consistent toast notifications
  const showToast = useCallback((title, description, status = "error", duration = 4000) => {
    toast({
      title,
      description,
      status,
      duration,
      position: "top",
      isClosable: true,
    });
  }, [toast]);

  // Handle form reset
  const handleFormReset = useCallback(() => {
    showToast(
      "Form Reset",
      "The form has been cleared. You can start over.",
      "info",
      2000
    );
  }, [showToast]);
  // Enhanced form submission handler
  const handleFormSubmit = useCallback(async (values, actions) => {
    try {
      setIsSubmitting(true);

      // Validate form before submission
      const errors = await actions.validateForm();
      if (Object.keys(errors).length > 0) {
        showToast(
          "Form Validation Error",
          "Please fill all required fields correctly before submitting.",
          "error"
        );
        return;
      }

      // Prepare request
      const payload = {
        ...values,
        userType: "academy",
        timestamp: new Date().toISOString(),
      };

      const response = await fetch(`${process.env.REACT_APP_BASE_URL}/api/contactUs`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      // Handle different response scenarios
      // Check if response is successful (200-299 status codes)
      if (response.ok) {
        // Your API returns the contact object directly on success
        // Check if the response contains the expected contact data
        if (result._id && result.firstName && result.email) {
          showToast(
            "Message Sent Successfully!",
            "Thank you for contacting us.",
            "success",
            5000
          );
          actions.resetForm();
        } else {
          // Fallback success check for different API response formats
          showToast(
            "Message Sent Successfully!",
            "Thank you for contacting us.",
            "success",
            5000
          );
          actions.resetForm();
        }
      } else if (response.status === 400) {
        // Handle validation errors from server
        const errorMessage = result.message || "Invalid form data. Please check your inputs.";
        showToast("Validation Error", errorMessage, "error");
      } else if (response.status === 429) {
        // Handle rate limiting
        showToast(
          "Rate Limit Exceeded",
          "You've sent too many messages. Please wait a few minutes before trying again.",
          "warning"
        );
      } else if (response.status >= 500) {
        // Handle server errors
        showToast(
          "Server Error",
          "Our servers are experiencing issues. Please try again in a few minutes.",
          "error"
        );
      } else {
        // Handle other errors
        const errorMessage = result.message || "Failed to send message. Please try again.";
        showToast("Message Failed", errorMessage, "error");
      }
    } catch (err) {
      console.error("Contact form submission error:", err);

      // Handle network errors
      if (err.name === "TypeError" && err.message.includes("fetch")) {
        showToast(
          "Network Error",
          "Unable to connect to our servers. Please check your internet connection and try again.",
          "error"
        );
      } else {
        showToast(
          "Unexpected Error",
          "Something went wrong while sending your message. Please try again.",
          "error"
        );
      }
    } finally {
      setIsSubmitting(false);
      actions.setSubmitting(false);
    }
  }, [showToast]);

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      email: "",
      mobile: "",
      message: "",
    },
    validationSchema: ContactSchema,
    onSubmit: handleFormSubmit,
  });

  // Enhanced field change handler that clears errors
  const handleFieldChange = useCallback((fieldName) => (e) => {
    formik.handleChange(e);

    // Clear field-specific error after user starts typing
    if (formik.errors[fieldName]) {
      formik.setFieldError(fieldName, "");
    }
  }, [formik]);

  // Enhanced field blur handler
  const handleFieldBlur = useCallback(() => (e) => {
    formik.handleBlur(e);
  }, [formik]);

  // Get field props for consistent styling
  const getFieldProps = useCallback((fieldName) => ({
    borderColor: (formik.errors[fieldName] && formik.touched[fieldName]) ? "red.300" : "gray.200",
    _hover: {
      borderColor: (formik.errors[fieldName] && formik.touched[fieldName]) ? "red.400" : "gray.300",
    },
    _focus: {
      borderColor: (formik.errors[fieldName] && formik.touched[fieldName]) ? "red.500" : "blue.500",
      boxShadow: (formik.errors[fieldName] && formik.touched[fieldName])
        ? "0 0 0 1px var(--chakra-colors-red-500)"
        : "0 0 0 1px var(--chakra-colors-blue-500)",
    },
  }), [formik.errors, formik.touched]);

  // Address multi-line helper
  const Address = chakra("span", {
    baseStyle: {
      display: "block",
      whiteSpace: "pre-line",
    },
  });

  return (
    <Layout title="Contact Us">
      <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
          className="p-0"
        >
        </Button>
        <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Contact</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
      </Flex>
      <Flex
        direction={isMobile ? "column" : "row"}
        gap={8}
        p={isMobile ? 1 : 8}
        bg="white"
        minH="80vh"
      >
        {/* Left column: contact details */}
        <Box flex={1} mb={isMobile ? 8 : 0} maxW="600px">
          <Heading size="lg" mb={4} color="gray.800">
            Get in touch
          </Heading>
          <Text fontSize="md" color="gray.600" mb={6} lineHeight={1.5}>
            We value your feedback and are committed to providing you with the best possible experience. Get in touch today and take the first step towards reaching your goals!
          </Text>
          <Stack spacing={5} fontSize="md">
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdLocationOn} boxSize={6} color="gray.400" />
              <Address>
                Umn Khel Shiksha Private Limited, Vasant Vihar,\nBasant Lok Complex, Road 21, New Delhi-110057
              </Address>
            </Flex>
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdPhone} boxSize={6} color="gray.400" />
              <Text as="a" href="tel:+919267986189">
                +91 92679 86189
              </Text>
            </Flex>
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdEmail} boxSize={6} color="gray.400" />
              <Text as="a" href="mailto:<EMAIL>">
                <EMAIL>
              </Text>
            </Flex>
            <Flex align="center" gap={3} color="gray.700">
              <Icon as={MdLanguage} boxSize={6} color="gray.400" />
              <Text
                as="a"
                href="https://khelcoach.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                khelcoach.com
              </Text>
            </Flex>
          </Stack>
        </Box>

        {/* Right column: contact form */}
        <Box flex={1} maxW="600px">
          <Box bg="gray.50" p={isMobile ? 4 : 8} borderRadius="lg" boxShadow="md">


            <Heading size="md" mb={4} color="gray.800">
              Send us a Message
            </Heading>
            <Text fontSize="sm" color="gray.600" mb={6}>
              Fill out the form below and we'll get back to you as soon as possible.
            </Text>

            <form onSubmit={formik.handleSubmit} noValidate>
              <Stack spacing={4}>
                <Flex gap={4} direction={isMobile ? "column" : "row"}>
                  <FormControl
                    isInvalid={formik.touched.firstName && !!formik.errors.firstName}
                  >
                    <FormLabel htmlFor="firstName">
                      First Name <Text as="span" color="red.500">*</Text>
                    </FormLabel>
                    <Input
                      id="firstName"
                      type="text"
                      name="firstName"
                      value={formik.values.firstName}
                      onChange={handleFieldChange("firstName")}
                      onBlur={handleFieldBlur()}
                      placeholder="Enter first name"
                      autoComplete="given-name"
                      {...getFieldProps("firstName")}
                    />
                    <FormErrorMessage>{formik.errors.firstName}</FormErrorMessage>
                  </FormControl>
                  <FormControl
                    isInvalid={formik.touched.lastName && !!formik.errors.lastName}
                  >
                    <FormLabel htmlFor="lastName">
                      Last Name <Text as="span" color="red.500">*</Text>
                    </FormLabel>
                    <Input
                      id="lastName"
                      type="text"
                      name="lastName"
                      value={formik.values.lastName}
                      onChange={handleFieldChange("lastName")}
                      onBlur={handleFieldBlur()}
                      placeholder="Enter last name"
                      autoComplete="family-name"
                      {...getFieldProps("lastName")}
                    />
                    <FormErrorMessage>{formik.errors.lastName}</FormErrorMessage>
                  </FormControl>
                </Flex>

                <FormControl isInvalid={formik.touched.email && !!formik.errors.email}>
                  <FormLabel htmlFor="email">
                    Email <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    value={formik.values.email}
                    onChange={handleFieldChange("email")}
                    onBlur={handleFieldBlur()}
                    placeholder="Enter your email address"
                    autoComplete="email"
                    {...getFieldProps("email")}
                  />
                  <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={formik.touched.mobile && !!formik.errors.mobile}>
                  <FormLabel htmlFor="mobile">
                    Phone Number <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <Input
                    id="mobile"
                    type="tel"
                    name="mobile"
                    value={formik.values.mobile}
                    onChange={handleFieldChange("mobile")}
                    onBlur={handleFieldBlur()}
                    placeholder="Enter your phone number"
                    autoComplete="tel"
                    {...getFieldProps("mobile")}
                  />
                  <FormErrorMessage>{formik.errors.mobile}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={formik.touched.message && !!formik.errors.message}>
                  <FormLabel htmlFor="message">
                    Message <Text as="span" color="red.500">*</Text>
                  </FormLabel>
                  <Textarea
                    id="message"
                    name="message"
                    value={formik.values.message}
                    onChange={handleFieldChange("message")}
                    onBlur={handleFieldBlur()}
                    placeholder="Enter your message (minimum 10 characters)"
                    rows={4}
                    resize="vertical"
                    {...getFieldProps("message")}
                  />
                  <FormErrorMessage>{formik.errors.message}</FormErrorMessage>
                  <Text fontSize="sm" color="gray.500" mt={1}>
                    {formik.values.message.length}/1000 characters
                  </Text>
                </FormControl>

                {/* Form Actions */}
                <Flex gap={3} mt={6} direction={isMobile ? "column" : "row"}>
                  <Button
                    type="submit"
                    colorScheme="telegram"
                    isLoading={isSubmitting || formik.isSubmitting}
                    loadingText="Sending Message..."
                    flex={1}
                    size="lg"
                    disabled={isSubmitting || formik.isSubmitting}
                    _disabled={{
                      opacity: 0.6,
                      cursor: "not-allowed",
                    }}
                  >
                    {isSubmitting || formik.isSubmitting ? "Sending Message..." : "Send Message"}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    colorScheme="gray"
                    onClick={() => {
                      formik.resetForm();
                      handleFormReset();
                    }}
                    disabled={isSubmitting || formik.isSubmitting}
                    size="lg"
                    flex={isMobile ? 1 : "none"}
                    minW={isMobile ? "auto" : "120px"}
                  >
                    Reset
                  </Button>
                </Flex>

                {/* Form submission info */}
                <Text fontSize="xs" color="gray.500" textAlign="center" mt={3}>
                  By submitting this form, you agree to our terms of service and privacy policy.
                </Text>
              </Stack>
            </form>
          </Box>
        </Box>
      </Flex>
    </Layout>
  );
}

export default Contact;
