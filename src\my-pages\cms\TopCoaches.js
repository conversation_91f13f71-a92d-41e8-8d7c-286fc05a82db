import React, { useEffect, useState } from "react";
import { useMediaQuery } from "@chakra-ui/react";
import Layout from "../../layout/default";
import { MdDelete, MdDragHandle } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  ListItem,
  UnorderedList,
  useToast,
  Spinner,
  Heading,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Badge,
  Tooltip,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import debounce from "../../utilities/debounce";
import { IoIosClose } from "react-icons/io";

const TopCoaches = () => {
  const navigate = useNavigate();
  const [coachData, setCoachData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const [cmsCoachData, setCmsCoachData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevCoachData, setPrevCoachData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleted, setIsDeleted] = useState(false);
  const [deleteCoachId, setDeleteCoachId] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteBtnLoading, setDeleteBtnLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCoachData = (query) => {
    setCoachData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach?${
        searchQuery.length > 0
          ? `page=1&firstName=${query}&authStatus=authorized&status=active`
          : "page=1&authStatus=authorized&status=active"
      }`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCoachData({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setCoachData({ result: [], isLoading: false, error: errorMessage });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: errorMessage,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCmsCoachData = () => {
    setCmsCoachData({ result: [], isLoading: true, error: false });

    // Decode token to get academyId
    const decodedToken = jwtDecode(token);
    const academyId = decodedToken.academyId._id;

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/${academyId}/coach`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCmsCoachData({
          result: response.data.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setCmsCoachData({ result: [], isLoading: false, error: errorMessage });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: errorMessage,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAndSave = (customCoachList = null) => {
    onClose1();
    setSaveChangesBtnLoading(true);
    const coachList = customCoachList || cmsCoachData.result;
    const getIds = coachList.map((id, index) => {
      return { coach: id?.coach?._id, position: index + 1 };
    });

    let data = JSON.stringify({
      documents: getIds,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/coach/add`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        getCmsCoachData();
        getCoachData(searchQuery);
        setSaveChangesBtnLoading(false);
        setIsUpdated(false);
        onClose();
        setIsDeleted(false);
        setDeleteCoachId(null);
        toast({
          title: "Coach updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setSaveChangesBtnLoading(false);
        if (
          error.response?.data?.message ===
          "Top Coach cannot be greater than 15"
        ) {
          getCmsCoachData();
          setIsUpdated(false);
          toast({
            title: error.response.data.message,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  // Delete Coach Modal logic
  const handleDeleteCoach = (coachId) => {
    setDeleteCoachId(coachId);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteCoach = () => {
    setDeleteBtnLoading(true);
    // Remove the coach from the list
    const updatedCoachList = cmsCoachData.result.filter(
      (item) => item?.coach?._id !== deleteCoachId
    );
    updateAndSave(updatedCoachList);
    setIsDeleteModalOpen(false);
    setDeleteBtnLoading(false);
  };
  // ...existing code...
  // Place this modal in your JSX (e.g., near the root return)
  {
    /* Delete Coach Confirmation Modal */
  }
  <Modal
    isOpen={isDeleteModalOpen}
    onClose={() => setIsDeleteModalOpen(false)}
    isCentered
  >
    <ModalOverlay />
    <ModalContent maxW={{ base: "90vw", md: "400px" }}>
      <ModalHeader>Delete Coach</ModalHeader>
      <ModalCloseButton />
      <ModalBody>Are you sure you want to delete this coach?</ModalBody>
      <ModalFooter>
        <Button onClick={() => setIsDeleteModalOpen(false)} mr={3}>
          Cancel
        </Button>
        <Button
          colorScheme="red"
          isLoading={deleteBtnLoading}
          onClick={confirmDeleteCoach}
        >
          Confirm
        </Button>
      </ModalFooter>
    </ModalContent>
  </Modal>;

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...cmsCoachData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setCmsCoachData({ ...cmsCoachData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = cmsCoachData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/coach/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevCoachData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPrevCoachData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getCmsCoachData();
  }, []);

  const getCoachDataRef = React.useRef(getCoachData);
  React.useEffect(() => {
    getCoachDataRef.current = getCoachData;
  }, [getCoachData]);

  const debouncedGetCoachData = React.useRef(
    debounce((query) => getCoachDataRef.current(query), 400)
  ).current;

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
    debouncedGetCoachData(e.target.value);
  };

  return (
    <Layout title="CMS | Coaches">
      {/* Responsive Back Button, Breadcrumb, and Action Buttons */}
      {isMobile ? (
        <Flex direction="column" alignItems="flex-start" gap={1} mb={3}>
          <Flex alignItems="center" gap={0} mb={{ base: 3, md: 0 }}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            ></Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Coaches</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Flex direction="row" gap={2} w="100%" justifyContent="flex-end">
              <Button
                variant={"outline"}
                colorScheme="telegram"
                size="xs"
                py={4}
                px={2}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  setAdjustBtnEdit(true);
                  setPrevCoachData(cmsCoachData.result);
                  getCmsCoachData();
                  setIsUpdated(false);
                }}
                mr={1}
                mb={0}
                isDisabled={adjustBtnEdit || cmsCoachData?.result?.length < 2}
              >
                Adjust Position
              </Button>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size="xs"
                py={4}
                px={2}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  onOpen();
                  setSearchQuery("");
                  getCoachData("");
                }}
                mr={0}
                mb={0}
                isDisabled={!cmsCoachData?.isLoading && cmsCoachData?.error}
              >
                Add Coach
              </Button>
            </Flex>
          )}
        </Flex>
      ) : (
        <Flex
          justifyContent={{ base: "flex-start", md: "space-between" }}
          alignItems={{ base: "flex-start", md: "center" }}
          direction={{ base: "column", md: "row" }}
          wrap="wrap"
          px={{ base: 0, md: 0 }}
        >
          <Flex alignItems="center" gap={0}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            ></Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Coaches</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {/* Responsive button group */}
          <Box mt={{ base: 2, md: 0 }}>
            {userData?.accessScopes?.cms?.includes("write") && (
              <>
                {isDeleted ? (
                  !(!cmsCoachData?.isLoading && cmsCoachData?.error) &&
                  !adjustBtnEdit && (
                    <Flex
                      justifyContent={{ base: "flex-start", md: "flex-end" }}
                      alignItems="center"
                      wrap="nowrap"
                      gap={{ base: 2, md: 4 }}
                      overflowX="auto"
                      whiteSpace="nowrap"
                    >
                      <Button
                        variant="outline"
                        colorScheme="red"
                        size={{ base: "xs", md: "sm" }}
                        py={{ base: 2, md: 5 }}
                        px={{ base: 2, md: 4 }}
                        isDisabled={!isUpdated}
                        onClick={() => {
                          setIsDeleted(false);
                          getCmsCoachData();
                          setIsUpdated(false);
                        }}
                        flexShrink={0}
                      >
                        Discard
                      </Button>
                      <Button
                        variant="outline"
                        colorScheme="green"
                        size={{ base: "xs", md: "sm" }}
                        py={{ base: 2, md: 5 }}
                        px={{ base: 2, md: 4 }}
                        isDisabled={!isUpdated}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                        flexShrink={0}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )
                ) : (
                  <Flex
                    justifyContent={{ base: "flex-start", md: "flex-start" }}
                    alignItems="center"
                    wrap="nowrap"
                    gap={{ base: 2, md: 3 }}
                    overflowX="auto"
                    whiteSpace="nowrap"
                  >
                    {!adjustBtnEdit ? (
                      <>
                        <Button
                          variant="outline"
                          colorScheme="telegram"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={{ base: 2, md: 4 }}
                          onClick={() => {
                            setAdjustBtnEdit(true);
                            setPrevCoachData(cmsCoachData.result);
                            getCmsCoachData();
                            setIsUpdated(false);
                          }}
                          flexShrink={0}
                          isDisabled={cmsCoachData?.result?.length < 2}
                        >
                          Adjust Position
                        </Button>

                        <Button
                          variant="outline"
                          colorScheme="teal"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={{ base: 2, md: 4 }}
                          onClick={() => {
                            onOpen();
                            setSearchQuery("");
                            getCoachData("");
                          }}
                          isDisabled={
                            !cmsCoachData?.isLoading && cmsCoachData?.error
                          }
                          flexShrink={0}
                        >
                          Add Coach
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          colorScheme="red"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={{ base: 2, md: 4 }}
                          onClick={() => {
                            setCmsCoachData({
                              result: prevCoachData,
                              isLoading: false,
                              error: false,
                            });
                            setAdjustBtnEdit(false);
                            getCmsCoachData();
                            setIsUpdated(false);
                          }}
                          flexShrink={0}
                        >
                          Discard
                        </Button>

                        <Button
                          variant="outline"
                          colorScheme="green"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={{ base: 2, md: 4 }}
                          isLoading={posBtnLoading}
                          onClick={updateBlockPosition}
                          flexShrink={0}
                        >
                          Save Changes
                        </Button>
                      </>
                    )}
                  </Flex>
                )}
              </>
            )}
          </Box>
        </Flex>
      )}
      {/* Added/Selected Coach List - Responsive */}
      {!cmsCoachData?.isLoading && cmsCoachData?.error ? (
        <Flex
          justifyContent={"center"}
          alignItems={"center"}
          w={"full"}
          my={10}
        >
          <Text color={"red.500"}>
            {cmsCoachData?.error}
          </Text>
        </Flex>
      ) : isMobile ? (
        <Box mt={4}>
          {cmsCoachData?.isLoading ? (
            <Flex justifyContent="center" alignItems="center" minH="120px">
              <Spinner />
            </Flex>
          ) : cmsCoachData?.result?.length > 0 ? (
            <Flex direction="column" gap={4}>
              {cmsCoachData.result.map((coachData, inx) => (
                <Card
                  key={inx}
                  variant="outline"
                  boxShadow="sm"
                  borderRadius="md"
                  draggable={adjustBtnEdit}
                  onDragStart={
                    adjustBtnEdit ? () => handleDragStart(inx) : undefined
                  }
                  onDragOver={
                    adjustBtnEdit ? () => handleDragOver(inx) : undefined
                  }
                  onDragEnd={adjustBtnEdit ? handleDragEnd : undefined}
                >
                  <CardBody>
                    {/* Coach Name */}
                    <Flex justify="space-between" align="center" mb="10px">
                      <Text fontWeight="semibold" fontSize="sm" mb={0}>
                        Coach Name:
                      </Text>
                      <Text fontSize="sm">
                        {coachData?.coach?.firstName +
                          " " +
                          coachData?.coach?.lastName}
                      </Text>
                    </Flex>
                    {/* Email */}
                    <Flex justify="space-between" align="center" mb="10px">
                      <Text fontWeight="semibold" fontSize="sm" mb={0}>
                        Email:
                      </Text>
                      <Text fontSize="sm">
                        {coachData?.coach?.email || "n/a"}
                      </Text>
                    </Flex>
                    {/* Phone */}
                    <Flex justify="space-between" align="center" mb="10px">
                      <Text fontWeight="semibold" fontSize="sm" mb={0}>
                        Phone:
                      </Text>
                      <Text fontSize="sm">
                        {coachData?.coach?.mobile || "n/a"}
                      </Text>
                    </Flex>
                    {/* Status */}
                    <Flex justify="space-between" align="center">
                      <Text fontWeight="semibold" fontSize="sm" mb={0}>
                        Status:
                      </Text>
                      <Text fontSize="sm">
                        {coachData?.coach?.status || "n/a"}
                      </Text>
                    </Flex>
                    <Flex justifyContent="space-between" alignItems="center" mt={2}>
                      <Text fontSize="sm" fontWeight="semibold" mb={0}>Action:</Text>
                      {!adjustBtnEdit ? (
                        userData?.accessScopes?.cms?.includes("delete") && (
                          <Flex
                            justifyContent="flex-end"
                            alignItems="center"
                            cursor="pointer"
                            onClick={() =>
                              handleDeleteCoach(coachData?.coach?._id)
                            }
                          >
                            <Text>
                              <MdDelete fontSize="18px" />
                            </Text>
                          </Flex>
                        )
                      ) : (
                        <Text as="span" ml={2}>
                          <MdDragHandle style={{ cursor: "grab" }} fontSize="24px" />
                        </Text>
                      )}
                    </Flex>
                  </CardBody>
                </Card>
              ))}
            </Flex>
          ) : (
            <Flex justifyContent="center" alignItems="center" w="full" my={10}>
              <Text color="gray.500" fontSize="md" fontWeight="semibold">
                No data to show
              </Text>
            </Flex>
          )}
        </Box>
      ) : (
        <TableContainer
          mt={{ base: 2, md: 6 }}
          height={{ base: "auto", md: `${window.innerHeight - 200}px` }}
          overflowY={{ base: "visible", md: "scroll" }}
          px={{ base: 1, md: 0 }}
        >
          <Table variant="simple" size={{ base: "sm", md: "md" }}>
            <Thead
              bgColor={"#c1eaee"}
              position={{ base: "static", md: "sticky" }}
              top={{ base: "auto", md: "0px" }}
              zIndex={"99"}
            >
              <Tr bgColor={"#E2DFDF"}>
                <Th fontSize={{ base: "xs", md: "xs" }}>Position</Th>
                <Th fontSize={{ base: "xs", md: "xs" }}>Coach Name</Th>
                <Th fontSize={{ base: "xs", md: "xs" }}>Email</Th>
                <Th fontSize={{ base: "xs", md: "xs" }}>Phone</Th>
                <Th fontSize={{ base: "xs", md: "xs" }}>Status</Th>
                <Th fontSize={{ base: "xs", md: "xs" }} textAlign={"center"}>
                  Action
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {cmsCoachData?.isLoading && !cmsCoachData?.error ? (
                <Tr>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                  <Td></Td>
                  <Td
                    display={{ base: "block", md: "flex" }}
                    justifyContent={{ base: "center", md: "flex-end" }}
                    alignItems={{ base: "center", md: "center" }}
                  >
                    <Spinner />
                  </Td>
                </Tr>
              ) : cmsCoachData?.result?.length === 0 ? (
                <Tr>
                  <Td colSpan={8} textAlign="center" py={10}>
                    <Text color="gray.500" fontSize="md" fontWeight="semibold">
                      No data to show
                    </Text>
                  </Td>
                </Tr>
              ) : (
                cmsCoachData?.result?.map((coachData, inx) => {
                  return (
                    <Tr
                      key={inx}
                      draggable={adjustBtnEdit}
                      onDragStart={
                        adjustBtnEdit ? () => handleDragStart(inx) : undefined
                      }
                      onDragOver={
                        adjustBtnEdit ? () => handleDragOver(inx) : undefined
                      }
                      onDragEnd={adjustBtnEdit ? handleDragEnd : undefined}
                    >
                      <Td fontSize={{ base: "xs", md: "14px" }}>
                        {inx + 1 + "."}
                      </Td>
                      <Td
                        fontSize={{ base: "xs", md: "14px" }}
                        style={{
                          whiteSpace: "pre-wrap",
                          wordWrap: "break-word",
                        }}
                      >
                        {coachData?.coach?.firstName +
                          " " +
                          coachData?.coach?.lastName}
                      </Td>
                      <Td fontSize={{ base: "xs", md: "14px" }}>
                        {coachData?.coach?.email}
                      </Td>
                      <Td fontSize={{ base: "xs", md: "14px" }}>
                        {coachData?.coach?.mobile}
                      </Td>
                      <Td fontSize={{ base: "xs", md: "14px" }}>
                        {coachData?.coach?.status}
                      </Td>
                      <Td fontSize={{ base: "xs", md: "14px" }}>
                        {!adjustBtnEdit ? (
                          userData?.accessScopes?.cms?.includes("delete") && (
                            <Flex
                              justifyContent={{
                                base: "flex-start",
                                md: "center",
                              }}
                              alignItems={{ base: "flex-start", md: "center" }}
                              cursor="pointer"
                              onClick={() =>
                                handleDeleteCoach(coachData?.coach?._id)
                              }
                            >
                              <Text>
                                <MdDelete
                                  fontSize={{ base: "18px", md: "24px" }}
                                />
                              </Text>
                            </Flex>
                          )
                        ) : (
                          <Flex
                            justifyContent={{
                              base: "flex-start",
                              md: "center",
                            }}
                            alignItems={{ base: "flex-start", md: "center" }}
                          >
                            <Text as={"span"} ml={3}>
                              <MdDragHandle
                                style={{ cursor: "grab" }}
                                fontSize={{ base: "18px", md: "24px" }}
                              />
                            </Text>
                          </Flex>
                        )}
                      </Td>
                    </Tr>
                  );
                })
              )}
            </Tbody>
          </Table>
        </TableContainer>
      )}

      {/* Modal for course search */}
      <Modal isOpen={isOpen} onClose={onClose} size={{ base: "sm", md: "5xl" }}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader px={{ base: 2, md: 6 }} py={2} position="relative">
            <Box position="absolute" top={0} right={2} zIndex={1}>
              <ModalCloseButton
                onClick={() => {
                  getCmsCoachData();
                  setIsUpdated(false);
                }}
              />
            </Box>
            <Flex direction="column" gap={3} mt={7} mr={4} ml={4}>
              <Text fontSize={{ base: "md", md: "xl" }} fontWeight="bold" mb={0}>
                Search Coach
              </Text>
              {!(!cmsCoachData?.isLoading && cmsCoachData?.error) &&
                !adjustBtnEdit &&
                userData?.accessScopes?.cms?.includes("write") && (
                  <Flex gap={2} justifyContent="flex-end">
                    <Button
                      variant={"outline"}
                      colorScheme="red"
                      size={{ base: "xs", md: "sm" }}
                      py={{ base: 4, md: 5 }}
                      px={{ base: 4, md: 6 }}
                      isDisabled={!isUpdated}
                      fontSize={{ base: "xs", md: "sm" }}
                      onClick={() => {
                        getCmsCoachData();
                        setIsUpdated(false);
                        onClose();
                      }}
                      minW={{ base: "80px", md: "100px" }}
                      h={{ base: "32px", md: "40px" }}
                    >
                      Discard
                    </Button>
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={{ base: "xs", md: "sm" }}
                      py={{ base: 4, md: 5 }}
                      px={{ base: 4, md: 6 }}
                      isDisabled={!isUpdated}
                      fontSize={{ base: "xs", md: "sm" }}
                      onClick={onOpen1}
                      isLoading={saveChangesBtnLoading}
                      minW={{ base: "100px", md: "120px" }}
                      h={{ base: "32px", md: "40px" }}
                    >
                      Save Changes
                    </Button>
                  </Flex>
                )}
            </Flex>
          </ModalHeader>

          <ModalBody>
            <Box mt={3} height={"95%"}>
              <Box>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <Input
                      type="text"
                      placeholder="Search Coach"
                      value={searchQuery}
                      onChange={handleSearchInputChange}
                      onKeyDown={(e) =>
                        e.key === "Enter" && getCoachData(searchQuery)
                      }
                    />
                    {searchQuery && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSearchQuery("");
                          getCoachData("");
                        }}
                        zIndex={1}
                        p={0}
                        minW={"auto"}
                        h={"auto"}
                      >
                        <IoIosClose fontSize="20px" />
                      </Button>
                    )}
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => getCoachData(searchQuery)}
                      isDisabled={!(searchQuery.length > 0)}
                    >
                      Search
                    </Button>
                  </Stack>
                  {/* Searched Course List */}
                  {!coachData?.isLoading && coachData?.error ? (
                    <Flex
                      justifyContent={"center"}
                      alignItems={"center"}
                      w={"full"}
                      my={10}
                    >
                      <Text color={"red.500"}>
                        {coachData?.error}
                      </Text>
                    </Flex>
                  ) : isMobile ? (
                    coachData.result.length > 0 ? (
                      <Box mt={4} px={1}>
                        <Flex direction="column" gap={3}>
                          {coachData.result.map((coachData, inx) => (
                            <Box
                              key={coachData?._id}
                              borderWidth="1px"
                              borderRadius="md"
                              boxShadow="sm"
                              bg="white"
                              p={3}
                            >
                              <Flex
                                justifyContent="space-between"
                                alignItems="center"
                                mb={1}
                              >
                                <Text fontWeight="bold" fontSize="sm">
                                  {(coachData?.firstName || "") +
                                    " " +
                                    (coachData?.lastName || "")}
                                </Text>
                              </Flex>
                              <Flex
                                alignItems="center"
                                justifyContent="space-between"
                                mb={1}
                              >
                                <Text
                                  fontSize="xs"
                                  color="gray.600"
                                  fontWeight="semibold"
                                >
                                  Status
                                </Text>
                                <Badge
                                  colorScheme={
                                    coachData?.status === "active"
                                      ? "green"
                                      : "red"
                                  }
                                  fontSize="0.7em"
                                >
                                  {coachData?.status?.toUpperCase()}
                                </Badge>
                              </Flex>
                              <Flex
                                alignItems="center"
                                justifyContent="space-between"
                                mb={1}
                              >
                                <Text
                                  fontSize="xs"
                                  color="gray.600"
                                  fontWeight="semibold"
                                >
                                  Auth Status
                                </Text>
                                <Badge
                                  colorScheme={
                                    coachData?.authStatus === "authorized"
                                      ? "green"
                                      : "red"
                                  }
                                  fontSize="0.7em"
                                >
                                  {coachData?.authStatus?.toUpperCase()}
                                </Badge>
                              </Flex>
                              <Flex justifyContent="flex-end" mt={2}>
                                {cmsCoachData?.result.every(
                                  (z) => z?.coach?._id !== coachData?._id
                                ) ? (
                                  <Button
                                    colorScheme="telegram"
                                    isDisabled={
                                      coachData?.status !== "active" ||
                                      coachData?.authStatus !== "authorized"
                                    }
                                    size="xs"
                                    fontSize="xs"
                                    onClick={() => {
                                      setIsUpdated(true);
                                      setCmsCoachData((prevState) => ({
                                        ...prevState,
                                        result: [
                                          ...prevState.result,
                                          { coach: coachData },
                                        ],
                                      }));
                                    }}
                                    w="35%"
                                  >
                                    Add
                                  </Button>
                                ) : (
                                  <Button
                                    colorScheme="red"
                                    size="xs"
                                    fontSize="xs"
                                    onClick={() => {
                                      setIsUpdated(true);
                                      setCmsCoachData((prevState) => ({
                                        ...prevState,
                                        result: prevState.result.filter(
                                          (obj) =>
                                            obj?.coach?._id !== coachData?._id
                                        ),
                                      }));
                                    }}
                                    w="35%"
                                  >
                                    Remove
                                  </Button>
                                )}
                              </Flex>
                            </Box>
                          ))}
                        </Flex>
                      </Box>
                    ) : coachData?.isLoading && !coachData?.error ? (
                      <Flex justifyContent="center" alignItems="center" mt={6}>
                        <Spinner size="lg" />
                      </Flex>
                    ) : (
                      <Flex justifyContent="center" alignItems="center" mt={6}>
                        <Text
                          fontSize="sm"
                          fontWeight="semibold"
                          color="red.300"
                        >
                          Result not found
                        </Text>
                      </Flex>
                    )
                  ) : coachData.result.length > 0 ? (
                    <TableContainer
                      mt={6}
                      height={`${window.innerHeight - 300}px`}
                      overflowY={"scroll"}
                    >
                      <Table variant="simple">
                        <Thead
                          bgColor={"#c1eaee"}
                          position={"sticky"}
                          top={"0px"}
                          zIndex={"99"}
                        >
                          <Tr bgColor={"#E2DFDF"}>
                            <Th>S.No</Th>
                            <Th>Coach Name</Th>
                            <Th>Status</Th>
                            <Th>Auth Status</Th>
                            <Th textAlign={"center"}>Action</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {coachData?.isLoading && !coachData?.error ? (
                            <Tr>
                              <Td></Td>
                              <Td> </Td>
                              <Td></Td>
                              <Td
                                display={"flex"}
                                justifyContent={"center"}
                                alignItems={"center"}
                              >
                                <Spinner />
                              </Td>
                              <Td></Td>
                              <Td></Td>
                              <Td></Td>
                            </Tr>
                          ) : (
                            coachData?.result?.map((coachData, inx) => (
                              <Tr key={coachData?._id}>
                                <Td>{inx + 1 + "."}</Td>
                                <Td
                                  style={{
                                    whiteSpace: "pre-wrap",
                                    wordWrap: "break-word",
                                  }}
                                >
                                  {(coachData?.firstName || "") +
                                    " " +
                                    (coachData?.lastName || "")}
                                </Td>
                                <Td>
                                  <Badge
                                    colorScheme={
                                      coachData?.status === "active"
                                        ? "green"
                                        : "red"
                                    }
                                  >
                                    {coachData?.status?.toUpperCase()}
                                  </Badge>
                                </Td>
                                <Td>
                                  <Badge
                                    colorScheme={
                                      coachData?.authStatus === "authorized"
                                        ? "green"
                                        : "red"
                                    }
                                  >
                                    {coachData?.authStatus?.toUpperCase()}
                                  </Badge>
                                </Td>
                                <Td textAlign="center">
                                  {cmsCoachData?.result.every(
                                    (z) => z?.coach?._id !== coachData?._id
                                  ) ? (
                                    <Button
                                      colorScheme="telegram"
                                      isDisabled={
                                        coachData?.status !== "active" ||
                                        coachData?.authStatus !== "authorized"
                                      }
                                      size={"sm"}
                                      w="80px"
                                      onClick={() => {
                                        setIsUpdated(true);
                                        setCmsCoachData((prevState) => ({
                                          ...prevState,
                                          result: [
                                            ...prevState.result,
                                            { coach: coachData },
                                          ],
                                        }));
                                      }}
                                    >
                                      Add
                                    </Button>
                                  ) : (
                                    <Button
                                      colorScheme="red"
                                      size={"sm"}
                                      w="80px"
                                      onClick={() => {
                                        setIsUpdated(true);
                                        setCmsCoachData((prevState) => ({
                                          ...prevState,
                                          result: prevState.result.filter(
                                            (obj) =>
                                              obj?.coach?._id !== coachData?._id
                                          ),
                                        }));
                                      }}
                                    >
                                      Remove
                                    </Button>
                                  )}
                                </Td>
                              </Tr>
                            ))
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  ) : coachData?.isLoading && !coachData?.error ? (
                    <Flex justifyContent="center" alignItems="center" mt={6}>
                      <Spinner size="lg" />
                    </Flex>
                  ) : (
                    <Flex justifyContent="center" alignItems="center" mt={6}>
                      <Text fontSize="sm" fontWeight="semibold" color="red.300">
                        Result not found
                      </Text>
                    </Flex>
                  )}
                </Box>
              </Box>
            </Box>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
        size={{ base: "sm", md: "md" }}
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={onClose1}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              onClick={() => updateAndSave()}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {/* Delete Coach Confirmation Dialog */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={() => setIsDeleteModalOpen(false)}
        isOpen={isDeleteModalOpen}
        isCentered
      >
        <AlertDialogOverlay />
        <AlertDialogContent maxW={{ base: "90vw", md: "400px" }}>
          <AlertDialogHeader>Delete Coach</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to remove this coach from Coaches?
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={() => setIsDeleteModalOpen(false)}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              isLoading={deleteBtnLoading}
              onClick={confirmDeleteCoach}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default TopCoaches;
