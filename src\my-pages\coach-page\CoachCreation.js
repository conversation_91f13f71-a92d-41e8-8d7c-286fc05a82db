import React from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Text,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import BasicDetailsCoach from "./BasicDetailsCoach";

const CoachCreation = () => {
  const navigate = useNavigate();
  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Create Training Schedule">
        <Flex justifyContent={"space-between"} alignItems={"center"} mb={3}>
          <Flex justifyContent={"center"} alignItems={"center"}>
            
              <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>

              <BreadcrumbItem>
                  <Link to="/coach-page">Coaches</Link>
              </BreadcrumbItem>

              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Create Coach</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
        </Flex>
          {/* Details component */}
          <BasicDetailsCoach coachData={null} />
      </Layout>
    </Box>
  );
};

export default CoachCreation;
