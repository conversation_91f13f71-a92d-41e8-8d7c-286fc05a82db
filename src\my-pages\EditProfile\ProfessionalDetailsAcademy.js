import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Card,
  CardBody,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  useToast,
  FormErrorMessage,
  ButtonGroup,
  Fade,
  ScaleFade,
  useColorModeValue,
  VStack,
  HStack,
  Tooltip,
  Image,
  Badge,
  Grid,
  Text,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
  IconButton,
} from "@chakra-ui/react";
import { LuCheck, LuPencilLine, LuX, LuPlus, LuTrash2, LuUpload } from "react-icons/lu";
import { useFormik } from "formik";
import axios from "axios";
import * as Yup from "yup";
import { State } from "country-state-city";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const ProfessionalDetailsAcademy = ({ academyData }) => {
  const [states, setStates] = useState([]);
  const [categories, setCategories] = useState([]);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSubmitBtnLoading, setIsSubmitBtnLoading] = useState(false);
  const [newSportCategory, setNewSportCategory] = useState("");
  const [sportsCategories, setSportsCategories] = useState(academyData?.sportsCategories || []);
  const [academyImages, setAcademyImages] = useState(academyData?.academyImages || []);
  const [linkedFacilities, setLinkedFacilities] = useState(academyData?.linkedFacilities || []);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [showAddFacilityForm, setShowAddFacilityForm] = useState(false);
  const [pincodeError, setPincodeError] = useState(false);
  const [facilityPincodeError, setFacilityPincodeError] = useState(false);
  const [facilityErrors, setFacilityErrors] = useState({}); // Track facility-specific errors
  const [newFacility, setNewFacility] = useState({
    name: "",
    addressLine1: "",
    addressLine2: "",
    city: "",
    state: "",
    pinCode: "",
    country: "India",
    amenities: ""
  });
  const toast = useToast();
  const adminToken = sessionStorage.getItem("admintoken");
  const token = adminToken ? adminToken.split(" ")[1] : null;

  // Color mode values for better theming - Greyish theme to match coach creation
  const cardBg = useColorModeValue("gray.50", "gray.800");
  const readOnlyBg = useColorModeValue("gray.100", "gray.700");
  const borderColor = useColorModeValue("gray.300", "gray.600");
  const textColor = useColorModeValue("gray.800", "gray.200");
  const labelColor = useColorModeValue("gray.700", "gray.300");

  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);

  // Fetch sports categories
  useEffect(() => {
    axios.get(`${process.env.REACT_APP_BASE_URL}/api/category`)
      .then(res => {
        setCategories(res.data.data || []);
      })
      .catch(err => {
        console.error("Failed to fetch categories:", err);
        toast({ 
          title: "Failed to fetch sports categories", 
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      });
  }, [toast]);

  // Function to get details from pincode for main office address
  const getDetailsFromPincode = async (pincode) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setPincodeError(true);
        return;
      }
      
      setPincodeError(false);
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setPincodeError(true);
        toast({
          title: "Invalid pincode",
          description: "Please enter a valid pincode",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        formik.setFieldError("pincode", "Pincode is not correct");
        formik.setFieldValue("city", "");
        formik.setFieldValue("state", "");
        return;
      } else {
        setPincodeError(false);
        const postOffice = details?.data[0]?.PostOffice[0];
        formik.setFieldValue("city", `${postOffice?.Name}, ${postOffice?.District}`);
        
        // Find state code from state name
        const stateData = states.find(state => state.name === postOffice?.State);
        formik.setFieldValue("state", stateData?.isoCode || "");
        formik.setFieldValue("country", "IN");
        
        toast({
          title: "Location details updated",
          description: "City, state, and country have been auto-filled",
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log("Pincode lookup error:", error);
      setPincodeError(true);
      toast({
        title: "Error fetching location details",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  // Function to get details from pincode for facility
  const getFacilityDetailsFromPincode = async (pincode) => {
    try {
      if (!pincode || pincode.length !== 6) {
        setFacilityPincodeError(true);
        return;
      }
      
      setFacilityPincodeError(false);
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        setFacilityPincodeError(true);
        toast({
          title: "Invalid pincode",
          description: "Please enter a valid pincode",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        setNewFacility(prev => ({ ...prev, city: "", state: "", country: "India" }));
        
        // Set validation errors for cleared city and state fields
        const newErrors = { ...facilityErrors };
        if (!newFacility.city) {
          newErrors.city = 'City is required';
        }
        if (!newFacility.state) {
          newErrors.state = 'State is required';
        }
        setFacilityErrors(newErrors);
        
        return;
      } else {
        setFacilityPincodeError(false);
        const postOffice = details?.data[0]?.PostOffice[0];
        const cityName = `${postOffice?.Name}, ${postOffice?.District}`;
        
        // Find state code from state name
        const stateData = states.find(state => state.name === postOffice?.State);
        
        setNewFacility(prev => ({
          ...prev,
          city: cityName,
          state: stateData?.isoCode || "",
          country: "India"
        }));
        
        // Clear validation errors for city and state since they were auto-filled successfully
        const clearedErrors = { ...facilityErrors };
        delete clearedErrors.city;
        delete clearedErrors.state;
        setFacilityErrors(clearedErrors);
        
        toast({
          title: "Facility location updated",
          description: "City, state, and country have been auto-filled",
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log("Facility pincode lookup error:", error);
      setFacilityPincodeError(true);
      toast({
        title: "Error fetching location details",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };


  // Function to get details from pincode for existing facility editing
  const getExistingFacilityDetailsFromPincode = async (pincode, facilityIndex) => {
    try {
     
      
      if (!pincode || pincode.length !== 6) {
        return;
      }
      
      const details = await axios.get(
        `https://api.postalpincode.in/pincode/${pincode}`
      );
      
      if (
        details?.data[0]?.Status === "Error" ||
        details?.data[0]?.Status === "404" ||
        details?.data[0]?.Message === "No records found"
      ) {
        toast({
          title: "Invalid pincode",
          description: "Please enter a valid pincode",
          status: "warning",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        return;
      } else {
        const postOffice = details?.data[0]?.PostOffice[0];
        const cityName = `${postOffice?.Name}, ${postOffice?.District}`;
        
        // Find state code from state name
        const stateData = states.find(state => state.name === postOffice?.State);
        
        // Update the specific facility in the linkedFacilities array
        const updatedFacilities = [...linkedFacilities];
    
        
        // Only update specific fields, preserve the parameter pincode
        updatedFacilities[facilityIndex].city = cityName;
        updatedFacilities[facilityIndex].state = stateData?.isoCode || "";
        updatedFacilities[facilityIndex].country = "India";
        // Explicitly set the pinCode to the parameter value to ensure accuracy
        updatedFacilities[facilityIndex].pinCode = pincode;
        
     
        
        setLinkedFacilities(updatedFacilities);
        
        toast({
          title: "Facility location updated",
          description: "City, state, and country have been auto-filled",
          status: "success",
          duration: 2000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log("Existing facility pincode lookup error:", error);
      toast({
        title: "Error fetching location details",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const validationSchema = Yup.object().shape({
    companyRegistrationNumber: Yup.string()
      .required("Company registration number is required")
      .min(3, "Must be at least 3 characters")
      .max(50, "Must be less than or equal to 50 characters"),
    officeAddress1: Yup.string().required("Office address 1 is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    pincode: Yup.string().matches(/^[0-9]{6}$/, "Invalid pincode"),
    country: Yup.string().required("Country is required"),
  });

  const formik = useFormik({
    initialValues: {
      companyRegistrationNumber: academyData?.companyRegistrationNumber || "",
      officeAddress1: academyData?.officeAddress?.addressLine1 || "",
      officeAddress2: academyData?.officeAddress?.addressLine2 || "",
      city: academyData?.officeAddress?.city || "",
      state: academyData?.officeAddress?.state || "",
      pincode: academyData?.officeAddress?.pinCode || "",
      country: academyData?.officeAddress?.country || "IN",
      sportsCategories: academyData?.sportsCategories || [],
      academyImages: academyData?.academyImages || [],
      linkedFacilities: academyData?.linkedFacilities || [],
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      setIsSubmitBtnLoading(true);
      

      
      // Check if form has validation errors
      if (!formik.isValid) {
        setIsSubmitBtnLoading(false);
        return;
      }
      
      try {
        // Prepare office address with required fields only, add optional fields if not empty
        const officeAddress = {
          addressLine1: values.officeAddress1,
          city: values.city,
          state: values.state,
          pinCode: values.pincode,
          country: values.country === "IN" || values.country === "India" ? "IN" : values.country,
        };
        
        // Only include addressLine2 if it's not empty
        if (values.officeAddress2 && values.officeAddress2.trim() !== '') {
          officeAddress.addressLine2 = values.officeAddress2;
        }

        const payload = {
          companyRegistrationNumber: values.companyRegistrationNumber,
          officeAddress: officeAddress,
          sportsCategories: sportsCategories,
          academyImages: academyImages,
          linkedFacilities: linkedFacilities
          .filter(facility => facility && facility.name && facility.addressLine1) // Only include facilities with required fields
          .map(facility => {
            // Destructure to separate _id and other fields - remove all metadata
            const { _id, location, __v, createdAt, updatedAt, ...cleanFacility } = facility;
            
            // Clean up facility data and ensure no undefined/null values
            const sanitizedFacility = {
              name: (cleanFacility.name || "").trim(),
              addressLine1: (cleanFacility.addressLine1 || "").trim(),
              city: (cleanFacility.city || "").trim(),
              state: (cleanFacility.state || "").trim(),
              pinCode: (cleanFacility.pinCode || ""),
              country: cleanFacility.country || "India"
            };
            
            // Only include addressLine2 if it's not empty
            if (cleanFacility.addressLine2 && cleanFacility.addressLine2.trim() !== '') {
              sanitizedFacility.addressLine2 = cleanFacility.addressLine2.trim();
            }
            
            // Only include amenities if it's not empty
            if (cleanFacility.amenities && cleanFacility.amenities.trim() !== '') {
              sanitizedFacility.amenities = cleanFacility.amenities.trim();
            }
            
            // Backend doesn't allow _id in the payload, so return only sanitized data
            // The backend will handle linking existing facilities by matching other fields
            return sanitizedFacility;
          })
          .filter(facility => facility.name && facility.addressLine1), // Final filter to ensure valid data
        };
        
       
        
        const response = await axios.patch(
          `${process.env.REACT_APP_BASE_URL}/api/academy/${academyData?._id}`,
          payload,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );


        if (response.data.status === "success") {
          toast({
            title: "Professional details updated successfully",
            status: "success",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
          setIsEditMode(false);
        } else {
          toast({
            title: response.data.message || "Something went wrong",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      } catch (error) {
        console.log("Professional Details API Error:", error);
       
        // Handle validation errors from backend
        if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
          const errorMessages = error.response.data.errors.map(err => {
            // Check if it's a facility-related error
            if ((err.field && err.field.includes('facility')) || (err.field && err.field.includes('amenities'))) {
              return `Facility ${err.field}: ${err.message || 'Validation error'}`;
            }
            return `${err.field}: ${err.message || 'Validation error'}`;
          });
          toast({
            title: "Validation Error",
            description: errorMessages.join(', '),
            status: "error",
            duration: 5000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response?.data?.message) {
          // Check if the error is specifically about facilities or amenities
          const errorMessage = error.response.data.message;
          if (errorMessage.toLowerCase().includes('facility') || errorMessage.toLowerCase().includes('amenities')) {
            toast({
              title: "Facility Error",
              description: errorMessage,
              status: "error",
              duration: 5000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: errorMessage,
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        } else if (error.response?.status === 400) {
          toast({
            title: "Bad Request",
            description: "Please check your facility data and try again. Amenities field is optional.",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response?.status === 422) {
          toast({
            title: "Validation Error",
            description: "Please check all facility fields. Note: Amenities are optional and can be left empty.",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong while updating professional details",
            description: "Please check your facility data and try again",
            status: "error",
            duration: 3000,
            position: "top",
            isClosable: true,
          });
        }
      } finally {
        setIsSubmitBtnLoading(false);
      }
    },
  });

  const handleEdit = () => {
    setIsEditMode(true);
  };

  const handleSave = () => {
    // Require at least one sports category
    if (!sportsCategories || sportsCategories.length === 0) {
      toast({
        title: "Sports Category Required",
        description: "Please select at least one sports category before saving.",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      // Set a visible error for the UI
      formik.setFieldError('sportsCategories', 'Please select at least one sports category');
      return;
    } else {
      formik.setFieldError('sportsCategories', undefined);
    }
    formik.handleSubmit();
  };

  const handleCancel = () => {
    formik.resetForm();
    setSportsCategories(academyData?.sportsCategories || []);
    setAcademyImages(academyData?.academyImages || []);
    setLinkedFacilities(academyData?.linkedFacilities || []);
    setNewSportCategory("");
    setShowAddFacilityForm(false);
    setNewFacility({
      name: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "India",
      amenities: ""
    });
    setIsEditMode(false);
  };

  // Function to validate facility fields
  const validateFacilityField = (fieldName, value) => {
    const newErrors = { ...facilityErrors };
    
    switch(fieldName) {
      case 'name':
        if (!value.trim()) {
          newErrors.name = 'Facility name is required';
        } else if (value.trim().length < 3) {
          newErrors.name = 'Facility name must be at least 3 characters';
        } else {
          delete newErrors.name;
        }
        break;
      case 'addressLine1':
        if (!value.trim()) {
          newErrors.addressLine1 = 'Address is required';
        } else if (value.trim().length < 10) {
          newErrors.addressLine1 = 'Address must be at least 10 characters';
        } else {
          delete newErrors.addressLine1;
        }
        break;
      case 'pinCode':
        if (!value.trim()) {
          newErrors.pinCode = 'Pincode is required';
        } else if (!/^[0-9]{6}$/.test(value)) {
          newErrors.pinCode = 'Pincode must be exactly 6 digits';
        } else {
          delete newErrors.pinCode;
        }
        break;
      case 'city':
        if (!value.trim()) {
          newErrors.city = 'City is required';
        } else {
          delete newErrors.city;
        }
        break;
      case 'state':
        if (!value.trim()) {
          newErrors.state = 'State is required';
        } else {
          delete newErrors.state;
        }
        break;
      case 'amenities':
        // Amenities is completely optional, no validation needed
        delete newErrors.amenities;
        break;
      default:
        break;
    }
    
    setFacilityErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Enhanced facility input handler with validation
  const handleFacilityInputChange = (fieldName, value) => {
    // Update facility data using functional update
    setNewFacility(prev => ({ ...prev, [fieldName]: value }));
    
    // Validate field immediately
    validateFacilityField(fieldName, value);
    
    // Special handling for pincode
    if (fieldName === 'pinCode') {
      // Only allow numeric input and limit to 6 digits
      if (!/^\d*$/.test(value) || value.length > 6) {
        return;
      }
      
      // Clear pincode error when user starts typing
      if (facilityPincodeError) {
        setFacilityPincodeError(false);
      }
      
      // Trigger API validation when exactly 6 digits
      if (value.length === 6) {
        getFacilityDetailsFromPincode(value);
      } else if (value.length !== 6) {
        setFacilityPincodeError(false);
      }
    }
  };

  const addNewFacility = () => {
    if (newFacility.name.trim() && newFacility.addressLine1.trim() && newFacility.city.trim() && newFacility.state.trim() && newFacility.pinCode) {
      const facilityToAdd = {
        name: newFacility.name.trim(),
        addressLine1: newFacility.addressLine1.trim(),
        addressLine2: newFacility.addressLine2.trim(),
        city: newFacility.city.trim(),
        state: newFacility.state.trim(),
        pinCode: newFacility.pinCode, // Don't trim pinCode to preserve exact value
        country: newFacility.country,
        amenities: newFacility.amenities.trim(),
      };
      setLinkedFacilities([...linkedFacilities, facilityToAdd]);
      setNewFacility({
        name: "",
        addressLine1: "",
        addressLine2: "",
        city: "",
        state: "",
        pinCode: "",
        country: "India",
        amenities: ""
      });
      setShowAddFacilityForm(false);
      toast({
        title: "Facility added successfully",
        description: "Don't forget to save your changes",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } else {
      toast({
        title: "Please fill all required fields",
        description: "Name, Address Line 1, City, State, and Pin Code are required",
        status: "warning",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
  };

  const cancelAddFacility = () => {
    setFacilityPincodeError(false);
    setNewFacility({
      name: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      pinCode: "",
      country: "India",
      amenities: ""
    });
    setShowAddFacilityForm(false);
  };

  const addSportCategory = () => {
    if (newSportCategory.trim() && !sportsCategories.includes(newSportCategory.trim())) {
      setSportsCategories([...sportsCategories, newSportCategory.trim()]);
      setNewSportCategory("");
    }
  };

  const removeSportCategory = (categoryToRemove) => {
    setSportsCategories(sportsCategories.filter(category => category !== categoryToRemove));
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "Please select a file less than 10 MB",
        status: "warning",
        duration: 5000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    setUploadingImage(true);
    try {
      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/academy/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        setAcademyImages([...academyImages, url]);
        toast({
          title: "Image uploaded successfully",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
      }
    } catch (error) {
      console.log(error);
      toast({
        title: "Error uploading image",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    } finally {
      setUploadingImage(false);
      e.target.value = "";
    }
  };

  const removeImage = (imageToRemove) => {
    setAcademyImages(academyImages.filter(image => image !== imageToRemove));
  };

  return (
    <VStack spacing={{ base: 4, md: 6 }} align="stretch">
      <ScaleFade initialScale={0.9} in={true}>
        <Card 
        bg={cardBg}
        shadow="xl"
        borderRadius="lg"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        transition="all 0.3s ease"
        _hover={{
          shadow: "2xl",
          transform: "translateY(-2px)"
        }}
      >
        <CardBody p={{ base: 4, md: 8 }}>
          <Flex justifyContent="space-between" alignItems="center" mb={{ base: 4, md: 6 }} direction={{ base: "column", md: "row" }} gap={{ base: 3, md: 0 }}>
            <HStack spacing={3}>
              <Heading size={{ base: "sm", md: "md" }} color="gray.600" fontWeight="bold">
                Professional Details
              </Heading>
            </HStack>
            <Fade in={true}>
              {!isEditMode ? (
                <Tooltip label="Edit professional information" placement="top">
                  <Button
                    colorScheme="gray"
                    variant="outline"
                    onClick={handleEdit}
                    leftIcon={<LuPencilLine />}
                    size={{ base: "sm", md: "md" }}
                   
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Edit
                  </Button>
                </Tooltip>
              ) : (
                <ButtonGroup spacing={{ base: 2, md: 3 }}>
                  <Button
                    colorScheme="blue"
                    onClick={handleSave}
                    isLoading={isSubmitBtnLoading}
                    leftIcon={<LuCheck />}
                    size={{ base: "sm", md: "md" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    colorScheme="red"
                    onClick={handleCancel}
                    leftIcon={<LuX />}
                    size={{ base: "sm", md: "md" }}
                    transition="all 0.2s"
                    _hover={{
                      transform: "scale(1.05)",
                      shadow: "md"
                    }}
                  >
                    Cancel
                  </Button>
                </ButtonGroup>
              )}
            </Fade>
          </Flex>
          
          <Divider borderColor={borderColor} mb={{ base: 6, md: 8 }} />
          
          <VStack spacing={{ base: 6, md: 8 }} align="stretch">
            {/* Company Registration Number Section */}
            <FormControl isInvalid={formik.errors.companyRegistrationNumber && formik.touched.companyRegistrationNumber}>
              <HStack spacing={3} mb={{ base: 3, md: 4 }}>
                
                <FormLabel fontWeight="semibold" color={labelColor} mb={0} fontSize={{ base: "sm", md: "md" }}>
                  Company Registration Number
                </FormLabel>
              </HStack>
              <Fade in={true}>
                {isEditMode ? (
                  <Input
                    type="text"
                    name="companyRegistrationNumber"
                    value={formik.values.companyRegistrationNumber}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter company registration number"
                    size={{ base: "sm", md: "md" }}
                    borderRadius="lg"
                    border="1px"
                    borderColor={borderColor}
                    transition="all 0.2s"
                    _hover={{
                      borderColor: "gray.400"
                    }}
                    _focus={{
                      borderColor: "gray.500",
                      shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                    }}
                  />
                ) : (
                  <Box
                    p={{ base: 2, md: 3 }}
                    borderWidth="1px"
                    borderRadius="lg"
                    bg={readOnlyBg}
                    color={textColor}
                    minH={{ base: "32px", md: "36px" }}
                    display="flex"
                    alignItems="center"
                    borderColor={borderColor}
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    transition="all 0.2s"
                    _hover={{
                      shadow: "sm"
                    }}
                  >
                    {formik.values.companyRegistrationNumber || "No registration number provided"}
                  </Box>
                )}
              </Fade>
              <FormErrorMessage>{formik.errors.companyRegistrationNumber}</FormErrorMessage>
            </FormControl>
            
            {/* Office Address Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Office Address
                </Heading>
              </HStack>
              
              <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* Address Line 1 */}
                <FormControl isInvalid={formik.errors.officeAddress1 && formik.touched.officeAddress1}>
                  <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                    Address Line 1
                  </FormLabel>
                  <Fade in={true}>
                    {isEditMode ? (
                      <Input
                        type="text"
                        name="officeAddress1"
                        value={formik.values.officeAddress1}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter address line 1"
                        size={{ base: "sm", md: "md" }}
                        borderRadius="lg"
                        border="1px"
                        borderColor={borderColor}
                        transition="all 0.2s"
                        _hover={{
                          borderColor: "gray.400"
                        }}
                        _focus={{
                          borderColor: "gray.500",
                          shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                        }}
                      />
                    ) : (
                      <Box
                        p={{ base: 2, md: 3 }}
                        borderWidth="1px"
                        borderRadius="lg"
                        bg={readOnlyBg}
                        color={textColor}
                        minH={{ base: "32px", md: "36px" }}
                        display="flex"
                        alignItems="center"
                        borderColor={borderColor}
                        fontSize={{ base: "sm", md: "md" }}
                        fontWeight="medium"
                        transition="all 0.2s"
                        _hover={{
                          shadow: "sm"
                        }}
                      >
                        {formik.values.officeAddress1 || "No address line 1 provided"}
                      </Box>
                    )}
                  </Fade>
                  <FormErrorMessage>{formik.errors.officeAddress1}</FormErrorMessage>
                </FormControl>

                {/* Address Line 2 */}
                <FormControl isInvalid={formik.errors.officeAddress2 && formik.touched.officeAddress2}>
                  <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                    Address Line 2 (Optional)
                  </FormLabel>
                  <Fade in={true}>
                    {isEditMode ? (
                      <Input
                        type="text"
                        name="officeAddress2"
                        value={formik.values.officeAddress2}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter address line 2 (optional)"
                        size={{ base: "sm", md: "md" }}
                        borderRadius="lg"
                        border="1px"
                        borderColor={borderColor}
                        transition="all 0.2s"
                        _hover={{
                          borderColor: "gray.400"
                        }}
                        _focus={{
                          borderColor: "gray.500",
                          shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                        }}
                      />
                    ) : (
                      <Box
                        p={{ base: 2, md: 3 }}
                        borderWidth="1px"
                        borderRadius="lg"
                        bg={readOnlyBg}
                        color={textColor}
                        minH={{ base: "32px", md: "36px" }}
                        display="flex"
                        alignItems="center"
                        borderColor={borderColor}
                        fontSize={{ base: "sm", md: "md" }}
                        fontWeight="medium"
                        transition="all 0.2s"
                        _hover={{
                          shadow: "sm"
                        }}
                      >
                        {formik.values.officeAddress2 || "No address line 2 provided"}
                      </Box>
                    )}
                  </Fade>
                  <FormErrorMessage>{formik.errors.officeAddress2}</FormErrorMessage>
                </FormControl>
              </VStack>
            </Box>
            
            {/* Location Details Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Location Details
                </Heading>
              </HStack>
              
              <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                {/* City and State in a row */}
                <Flex gap={{ base: 4, md: 6 }} direction={{ base: "column", md: "row" }}>
                  <FormControl isInvalid={formik.errors.city && formik.touched.city} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      City
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Input
                          type="text"
                          name="city"
                          value={formik.values.city}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="Enter city"
                          size={{ base: "sm", md: "md" }}
                          borderRadius="lg"
                          border="1px"
                          borderColor={borderColor}
                          transition="all 0.2s"
                          _hover={{
                            borderColor: "gray.400"
                          }}
                          _focus={{
                            borderColor: "gray.500",
                            shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                          }}
                        />
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {formik.values.city || "No city provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.city}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={formik.errors.state && formik.touched.state} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      State
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Select
                          name="state"
                          value={formik.values.state}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          placeholder="Select state"
                          size={{ base: "sm", md: "md" }}
                          borderRadius="lg"
                          border="1px"
                          borderColor={borderColor}
                          transition="all 0.2s"
                          _hover={{
                            borderColor: "gray.400"
                          }}
                          _focus={{
                            borderColor: "gray.500",
                            shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                          }}
                        >
                          {states.map((state, index) => (
                            <option key={index} value={state.isoCode}>
                              {state.name}
                            </option>
                          ))}
                        </Select>
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {states.find(state => state.isoCode === formik.values.state)?.name || "No state provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.state}</FormErrorMessage>
                  </FormControl>
                </Flex>

                {/* Pincode and Country in a row */}
                <Flex gap={{ base: 4, md: 6 }} direction={{ base: "column", md: "row" }}>
                  <FormControl isInvalid={(formik.errors.pincode && formik.touched.pincode) || pincodeError} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      Pincode
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Input
                          type="text"
                          name="pincode"
                          value={formik.values.pincode}
                          onChange={(e) => {
                            const newValue = e.target.value;
                            // Only allow numeric input and limit to reasonable length
                            if (/^\d*$/.test(newValue) && newValue.length <= 8) {
                              formik.handleChange(e);
                              
                              // Trigger validation when user types exactly 6 digits
                              if (newValue.length === 6) {
                                getDetailsFromPincode(newValue);
                              } else if (newValue.length !== 6) {
                                // Reset error state if pincode is not 6 digits
                                setPincodeError(false);
                                formik.setFieldError("pincode", "");
                              }
                            }
                          }}
                          onBlur={(e) => {
                            formik.handleBlur(e);
                            if (e.target.value && e.target.value.length === 6) {
                              getDetailsFromPincode(e.target.value);
                            }
                          }}
                          placeholder="Enter pincode"
                          size={{ base: "sm", md: "md" }}
                          borderRadius="lg"
                          border="1px"
                          borderColor={borderColor}
                          transition="all 0.2s"
                          _hover={{
                            borderColor: "gray.400"
                          }}
                          _focus={{
                            borderColor: "gray.500",
                            shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                          }}
                        />
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {formik.values.pincode || "No pincode provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>
                      {formik.errors.pincode || (pincodeError && "Invalid pincode - please enter a valid 6-digit pincode")}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={formik.errors.country && formik.touched.country} flex={1}>
                    <FormLabel fontWeight="semibold" color={labelColor} fontSize={{ base: "sm", md: "md" }}>
                      Country
                    </FormLabel>
                    <Fade in={true}>
                      {isEditMode ? (
                        <Select
                          name="country"
                          value={formik.values.country}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          size={{ base: "sm", md: "md" }}
                          borderRadius="lg"
                          border="1px"
                          borderColor={borderColor}
                          transition="all 0.2s"
                          _hover={{
                            borderColor: "gray.400"
                          }}
                          _focus={{
                            borderColor: "gray.500",
                            shadow: "0 0 0 1px var(--chakra-colors-gray-500)"
                          }}
                        >
                          <option value="IN">India</option>
                        </Select>
                      ) : (
                        <Box
                          p={{ base: 2, md: 3 }}
                          borderWidth="1px"
                          borderRadius="lg"
                          bg={readOnlyBg}
                          color={textColor}
                          minH={{ base: "32px", md: "36px" }}
                          display="flex"
                          alignItems="center"
                          borderColor={borderColor}
                          fontSize={{ base: "sm", md: "md" }}
                          fontWeight="medium"
                          transition="all 0.2s"
                          _hover={{
                            shadow: "sm"
                          }}
                        >
                          {formik.values.country === "IN" ? "India" : formik.values.country || "No country provided"}
                        </Box>
                      )}
                    </Fade>
                    <FormErrorMessage>{formik.errors.country}</FormErrorMessage>
                  </FormControl>
                </Flex>
              </VStack>
            </Box>

            {/* Sports Categories Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Sports Categories
                </Heading>
              </HStack>
              
              <Box
                p={{ base: 2, md: 3 }}
                borderWidth="1px"
                borderRadius="lg"
                bg={readOnlyBg}
                borderColor={borderColor}
                transition="all 0.2s"
                _hover={{
                  shadow: "sm"
                }}
              >
                {isEditMode ? (
                  <VStack spacing={4} align="stretch">
                    <Flex direction={{ base: "column", md: "row" }} gap={{ base: 3, md: 0 }}>
                      <Select
                        placeholder="Select sport category to add"
                        value={newSportCategory}
                        onChange={(e) => setNewSportCategory(e.target.value)}
                        size={{ base: "sm", md: "md" }}
                        borderRadius="lg"
                        flex={1}
                        mr={{ base: 0, md: 3 }}
                      >
                        {categories
                          .filter(category => !sportsCategories.includes(category.name))
                          .map((category) => (
                            <option key={category._id} value={category.name}>
                              {category.name}
                            </option>
                          ))}
                      </Select>
                      <Button
                        colorScheme="gray"
                        onClick={addSportCategory}
                        leftIcon={<LuPlus />}
                        size={{ base: "sm", md: "md" }}
                        isDisabled={!newSportCategory}
                        minW={{ base: "full", md: "auto" }}
                      >
                        Add
                      </Button>
                    </Flex>
                    {sportsCategories.length > 0 ? (
                      <Wrap spacing={2}>
                        {sportsCategories.map((sport, index) => (
                          <WrapItem key={index}>
                            <Tag
                              size={{ base: "sm", md: "md" }}
                              borderRadius="full"
                              variant="solid"
                              colorScheme="gray"
                            >
                              <TagLabel>{sport}</TagLabel>
                              <TagCloseButton onClick={() => removeSportCategory(sport)} />
                            </Tag>
                          </WrapItem>
                        ))}
                      </Wrap>
                    ) : (
                      <Text color={textColor} fontSize={{ base: "sm", md: "md" }} fontStyle="italic">
                        No sports categories added yet
                      </Text>
                    )}
                    {/* Show Formik error for sportsCategories below the field */}
                    {formik.errors.sportsCategories && (
                      <Text color="red.500" fontSize={{ base: "sm", md: "md" }}>
                        {formik.errors.sportsCategories}
                      </Text>
                    )}
                  </VStack>
                ) : (
                  <>
                    {sportsCategories.length > 0 ? (
                      <Flex wrap="wrap" gap={2}>
                        {sportsCategories.map((sport, index) => (
                          <Badge
                            key={index}
                            colorScheme="gray"
                            variant="subtle"
                            px={3}
                            py={1}
                            borderRadius="full"
                            fontSize={{ base: "xs", md: "sm" }}
                            fontWeight="medium"
                          >
                            {sport}
                          </Badge>
                        ))}
                      </Flex>
                    ) : (
                      <Text color={textColor} fontSize={{ base: "sm", md: "md" }} fontWeight="medium">
                        No sports categories specified
                      </Text>
                    )}
                  </>
                )}
              </Box>
            </Box>

            {/* Academy Images Section */}
            <Box>
              <HStack spacing={3} mb={{ base: 4, md: 6 }}>
                
                <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="semibold">
                  Academy Images
                </Heading>
              </HStack>
              
              <Box
                p={{ base: 2, md: 3 }}
                borderWidth="1px"
                borderRadius="lg"
                bg={readOnlyBg}
                borderColor={borderColor}
                transition="all 0.2s"
                _hover={{
                  shadow: "sm"
                }}
              >
                {isEditMode && (
                  <VStack spacing={4} align="stretch">
                    <Input
                      type="file"
                      id="academyImageInput"
                      onChange={handleImageUpload}
                      style={{ display: "none" }}
                      accept="image/*"
                      multiple={false}
                    />
                    <Button
                      onClick={() => document.getElementById('academyImageInput').click()}
                      colorScheme="gray"
                      variant="outline"
                      leftIcon={<LuUpload />}
                      isLoading={uploadingImage}
                      loadingText="Uploading..."
                      size={{ base: "sm", md: "md" }}
                    >
                      Upload New Image
                    </Button>
                  </VStack>
                )}

                {academyImages.length > 0 ? (
                  <Grid 
                    templateColumns={{ base: "repeat(auto-fill, minmax(40px, 1fr))", md: "repeat(auto-fill, minmax(50px, 1fr))" }}
                    gap={{ base: 2, md: 3 }}
                    w="full"
                    mt={isEditMode ? 4 : 0}
                  >
                    {academyImages.map((imageUrl, index) => (
                      <Box key={index} position="relative">
                        <Image
                          src={imageUrl}
                          alt={`Academy image ${index + 1}`}
                          boxSize={{ base: "40px", md: "50px" }}
                          objectFit="cover"
                          borderRadius="lg"
                          border="1px"
                          borderColor="gray.300"
                          transition="all 0.2s"
                          _hover={{
                            borderColor: "gray.500",
                            transform: "scale(1.1)"
                          }}
                          cursor="pointer"
                          onClick={() => window.open(imageUrl, '_blank')}
                        />
                        {isEditMode && (
                          <IconButton
                            icon={<LuTrash2 />}
                            size="xs"
                            colorScheme="red"
                            variant="solid"
                            position="absolute"
                            top="-5px"
                            right="-5px"
                            borderRadius="full"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeImage(imageUrl);
                            }}
                            aria-label="Remove image"
                          />
                        )}
                      </Box>
                    ))}
                  </Grid>
                ) : (
                  <Text 
                    color={textColor} 
                    fontSize={{ base: "sm", md: "md" }}
                    fontWeight="medium"
                    mt={isEditMode ? 4 : 0}
                  >
                    No academy images uploaded
                  </Text>
                )}
              </Box>
            </Box>
          </VStack>
        </CardBody>
      </Card>

      {/* Linked Facilities Section - Separate Card */}
      <Card 
        bg={cardBg}
        shadow="xl"
        borderRadius="lg"
        border="1px"
        borderColor={borderColor}
        overflow="hidden"
        transition="all 0.3s ease"
        _hover={{
          shadow: "2xl",
          transform: "translateY(-2px)"
        }}
        mt={{ base: 4, md: 6 }}
      >
        <CardBody p={{ base: 4, md: 8 }}>
          <Flex justifyContent="space-between" alignItems="center" mb={{ base: 4, md: 6 }} direction={{ base: "column", md: "row" }} gap={{ base: 3, md: 0 }}>
            <HStack spacing={3}>
              
              <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="bold">
                Facility
              </Heading>
            </HStack>
          </Flex>
          
          <Divider borderColor={borderColor} mb={{ base: 6, md: 8 }} />
          
          {/* Add New Facility Button in Edit Mode */}
          {isEditMode && (
            <Box mb={{ base: 4, md: 6 }}>
              {!showAddFacilityForm ? (
                <Button
                  colorScheme="gray"
                  variant="outline"
                  leftIcon={<LuPlus />}
                  size={{ base: "sm", md: "md" }}
                  borderStyle="dashed"
                  borderWidth="1px"
                  py={{ base: 2, md: 3 }}
                  fontSize={{ base: "xs", md: "sm" }}
                  fontWeight="semibold"
                  transition="all 0.3s ease"
                  _hover={{
                    borderColor: "gray.400",
                    bg: "gray.50",
                    transform: "translateY(-1px)",
                    shadow: "md"
                  }}
                  onClick={() => setShowAddFacilityForm(true)}
                  w={{ base: "full", md: "auto" }}
                >
                  Add New Facility
                </Button>
              ) : (
                <Card
                  bg={cardBg}
                  shadow="md"
                  borderRadius="lg"
                  border="1px"
                  borderColor="gray.200"
                  borderStyle="dashed"
                >
                  <CardBody p={{ base: 4, md: 6 }}>
                    <VStack spacing={4} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Heading size={{ base: "xs", md: "sm" }} color="gray.600" fontWeight="bold">
                          Add New Facility
                        </Heading>
                        <IconButton
                          icon={<LuX />}
                          size="sm"
                          variant="ghost"
                          colorScheme="red"
                          onClick={cancelAddFacility}
                          aria-label="Cancel adding facility"
                        />
                      </HStack>
                      
                      <Divider borderColor="gray.200" />
                      
                      <VStack spacing={4} align="stretch">
                        {/* Facility Name */}
                        <FormControl isRequired isInvalid={facilityErrors.name}>
                          <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                            Facility Name
                          </FormLabel>
                          <Input
                            value={newFacility.name}
                            onChange={(e) => handleFacilityInputChange('name', e.target.value)}
                            placeholder="Enter facility name"
                            size={{ base: "xs", md: "sm" }}
                            borderRadius="lg"
                            borderColor={facilityErrors.name ? "red.500" : "gray.300"}
                            _focus={{
                              borderColor: facilityErrors.name ? "red.500" : "gray.500",
                              shadow: facilityErrors.name ? "0 0 0 1px red" : "0 0 0 1px gray"
                            }}
                          />
                          {facilityErrors.name && (
                            <FormErrorMessage fontSize="xs">
                              {facilityErrors.name}
                            </FormErrorMessage>
                          )}
                        </FormControl>

                        {/* Address Lines */}
                        <FormControl isRequired isInvalid={facilityErrors.addressLine1}>
                          <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                            Address Line 1
                          </FormLabel>
                          <Input
                            value={newFacility.addressLine1}
                            onChange={(e) => handleFacilityInputChange('addressLine1', e.target.value)}
                            placeholder="Enter address line 1"
                            size={{ base: "xs", md: "sm" }}
                            borderRadius="lg"
                            borderColor={facilityErrors.addressLine1 ? "red.500" : "gray.300"}
                            _focus={{
                              borderColor: facilityErrors.addressLine1 ? "red.500" : "gray.500",
                              shadow: facilityErrors.addressLine1 ? "0 0 0 1px red" : "0 0 0 1px gray"
                            }}
                          />
                          {facilityErrors.addressLine1 && (
                            <FormErrorMessage fontSize="xs">
                              {facilityErrors.addressLine1}
                            </FormErrorMessage>
                          )}
                        </FormControl>

                        <FormControl>
                          <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                            Address Line 2 (Optional)
                          </FormLabel>
                          <Input
                            value={newFacility.addressLine2}
                            onChange={(e) => setNewFacility({ ...newFacility, addressLine2: e.target.value })}
                            placeholder="Enter address line 2 (optional)"
                            size={{ base: "xs", md: "sm" }}
                            borderRadius="lg"
                          />
                        </FormControl>

                        {/* Location Details */}
                        <Flex gap={{ base: 2, md: 3 }} direction={{ base: "column", md: "row" }}>
                          <FormControl isRequired flex={1} isInvalid={facilityErrors.city}>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              City
                            </FormLabel>
                            <Input
                              value={newFacility.city}
                              onChange={(e) => handleFacilityInputChange('city', e.target.value)}
                              placeholder="Enter city"
                              size={{ base: "xs", md: "sm" }}
                              borderRadius="lg"
                              borderColor={facilityErrors.city ? "red.500" : "gray.300"}
                              _focus={{
                                borderColor: facilityErrors.city ? "red.500" : "gray.500",
                                shadow: facilityErrors.city ? "0 0 0 1px red" : "0 0 0 1px gray"
                              }}
                            />
                            {facilityErrors.city && (
                              <FormErrorMessage fontSize="xs">
                                {facilityErrors.city}
                              </FormErrorMessage>
                            )}
                          </FormControl>

                          <FormControl isRequired flex={1} isInvalid={facilityErrors.state}>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              State
                            </FormLabel>
                            <Select
                              value={newFacility.state}
                              onChange={(e) => handleFacilityInputChange('state', e.target.value)}
                              placeholder="Select state"
                              size={{ base: "xs", md: "sm" }}
                              borderRadius="lg"
                              borderColor={facilityErrors.state ? "red.500" : "gray.300"}
                              _focus={{
                                borderColor: facilityErrors.state ? "red.500" : "gray.500",
                                shadow: facilityErrors.state ? "0 0 0 1px red" : "0 0 0 1px gray"
                              }}
                            >
                              {states.map((state, index) => (
                                <option key={index} value={state.isoCode}>
                                  {state.name}
                                </option>
                              ))}
                            </Select>
                            {facilityErrors.state && (
                              <FormErrorMessage fontSize="xs">
                                {facilityErrors.state}
                              </FormErrorMessage>
                            )}
                          </FormControl>
                        </Flex>

                        <Flex gap={{ base: 2, md: 3 }} direction={{ base: "column", md: "row" }}>
                          <FormControl isRequired flex={1} isInvalid={facilityPincodeError}>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              Pin Code
                            </FormLabel>
                            <Input
                              value={newFacility.pinCode}
                              onChange={(e) => {
                                const newValue = e.target.value;
                                // Only allow numeric input and limit to reasonable length
                                if (/^\d*$/.test(newValue) && newValue.length <= 8) {
                                  setNewFacility(prev => ({ ...prev, pinCode: newValue }));
                                  
                                  // Clear error state when user starts typing
                                  if (facilityPincodeError) {
                                    setFacilityPincodeError(false);
                                  }
                                  
                                  // Trigger validation when user types exactly 6 digits
                                  if (newValue.length === 6) {
                                    getFacilityDetailsFromPincode(newValue);
                                  } else if (newValue.length !== 6) {
                                    // Reset error state if pincode is not 6 digits
                                    setFacilityPincodeError(false);
                                  }
                                }
                              }}
                              onBlur={(e) => {
                                if (e.target.value && e.target.value.length === 6) {
                                  getFacilityDetailsFromPincode(e.target.value);
                                }
                              }}
                              placeholder="Enter pin code"
                              size={{ base: "xs", md: "sm" }}
                              borderRadius="lg"
                              borderColor={facilityPincodeError ? "red.500" : "gray.300"}
                              _focus={{
                                borderColor: facilityPincodeError ? "red.500" : "gray.500",
                                shadow: facilityPincodeError ? "0 0 0 1px red" : "0 0 0 1px gray"
                              }}
                            />
                            {facilityPincodeError && (
                              <FormErrorMessage fontSize="xs">
                                Invalid pincode - please enter a valid 6-digit pincode
                              </FormErrorMessage>
                            )}
                          </FormControl>

                          <FormControl flex={1}>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              Country
                            </FormLabel>
                            <Select
                              value={newFacility.country}
                              onChange={(e) => setNewFacility({ ...newFacility, country: e.target.value })}
                              size={{ base: "xs", md: "sm" }}
                              borderRadius="lg"
                            >
                              <option value="India">India</option>
                            </Select>
                          </FormControl>
                        </Flex>

                        {/* Amenities */}
                        <FormControl>
                          <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                            Amenities (Optional)
                          </FormLabel>
                          <Box
                            borderWidth="1px"
                            borderColor="gray.300"
                            borderRadius="lg"
                            bg="white"
                          >
                            <ReactQuill
                              value={newFacility.amenities}
                              onChange={(content) => setNewFacility({ ...newFacility, amenities: content })}
                              placeholder="Describe available amenities..."
                              theme="snow"
                              modules={{
                                toolbar: [
                                  [{ 'header': [1, 2, 3, false] }],
                                  ['bold', 'italic', 'underline', 'strike'],
                                  [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                  [{ 'color': [] }, { 'background': [] }],
                                  ['link'],
                                  ['clean']
                                ],
                              }}
                              formats={[
                                'header', 'bold', 'italic', 'underline', 'strike',
                                'list', 'bullet', 'color', 'background', 'link'
                              ]}
                              style={{
                                backgroundColor: 'white',
                                minHeight: '80px',
                                fontSize: '14px'
                              }}
                            />
                          </Box>
                        </FormControl>

                        {/* Action Buttons */}
                        <HStack spacing={{ base: 2, md: 3 }} pt={2} direction={{ base: "column", md: "row" }}>
                          <Button
                            colorScheme="gray"
                            size={{ base: "xs", md: "sm" }}
                            leftIcon={<LuCheck />}
                            onClick={addNewFacility}
                            flex={1}
                            w={{ base: "full", md: "auto" }}
                          >
                            Add Facility
                          </Button>
                          <Button
                            variant="outline"
                            colorScheme="red"
                            size={{ base: "xs", md: "sm" }}
                            leftIcon={<LuX />}
                            onClick={cancelAddFacility}
                            flex={1}
                            w={{ base: "full", md: "auto" }}
                          >
                            Cancel
                          </Button>
                        </HStack>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              )}
            </Box>
          )}
          
          {linkedFacilities.length > 0 ? (
            <VStack spacing={{ base: 3, md: 4 }} align="stretch">
              {linkedFacilities.map((facility, index) => (
                <Card
                  key={index}
                  bg={cardBg}
                  shadow="md"
                  borderRadius="lg"
                  border="1px"
                  borderColor={borderColor}
                  overflow="hidden"
                  transition="all 0.3s ease"
                  _hover={{
                    shadow: "lg",
                    transform: "translateY(-1px)"
                  }}
                >
                  <CardBody p={{ base: 4, md: 6 }}>
                    <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                      <HStack justify="space-between" align="center" direction={{ base: "column", md: "row" }} spacing={{ base: 2, md: 0 }}>
                        <HStack spacing={3}>
                          
                          {isEditMode ? (
                            <FormControl isInvalid={facilityErrors[index]?.name}>
                              <Input
                                value={facility.name}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...updatedFacilities[index], name: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                  
                                  // Clear specific field error
                                  if (facilityErrors[index]?.name) {
                                    setFacilityErrors(prev => ({
                                      ...prev,
                                      [index]: { ...prev[index], name: '' }
                                    }));
                                  }
                                }}
                                placeholder="Enter facility name"
                                size={{ base: "xs", md: "sm" }}
                                borderRadius="lg"
                                bg={cardBg}
                                fontWeight="bold"
                                color="gray.600"
                              />
                              {facilityErrors[index]?.name && (
                                <FormErrorMessage fontSize="xs">
                                  {facilityErrors[index].name}
                                </FormErrorMessage>
                              )}
                            </FormControl>
                          ) : (
                            <Heading size={{ base: "sm", md: "md" }} color="gray.600" fontWeight="bold">
                              {facility.name}
                            </Heading>
                          )}
                        </HStack>
                        {isEditMode && (
                          <IconButton
                            icon={<LuTrash2 />}
                            size="sm"
                            colorScheme="red"
                            variant="outline"
                            onClick={() => {
                              setLinkedFacilities(linkedFacilities.filter((_, i) => i !== index));
                              // Clear errors for this facility
                              if (facilityErrors[index]) {
                                const newErrors = { ...facilityErrors };
                                delete newErrors[index];
                                setFacilityErrors(newErrors);
                              }
                            }}
                            aria-label="Remove facility"
                          />
                        )}
                      </HStack>
                      
                      <Divider borderColor={borderColor} />
                      
                      {isEditMode ? (
                        <VStack spacing={{ base: 3, md: 4 }} align="stretch">
                          {/* Address Line 1 */}
                          <FormControl isInvalid={facilityErrors[index]?.addressLine1}>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              Address Line 1 <Text as="span" color="red.500">*</Text>
                            </FormLabel>
                            <Input
                              value={facility.addressLine1}
                              onChange={(e) => {
                                const updatedFacilities = [...linkedFacilities];
                                updatedFacilities[index] = { ...updatedFacilities[index], addressLine1: e.target.value };
                                setLinkedFacilities(updatedFacilities);
                                
                                if (facilityErrors[index]?.addressLine1) {
                                  setFacilityErrors(prev => ({
                                    ...prev,
                                    [index]: { ...prev[index], addressLine1: '' }
                                  }));
                                }
                              }}
                              placeholder="Enter address line 1"
                              size={{ base: "xs", md: "sm" }}
                              borderRadius="lg"
                            />
                            {facilityErrors[index]?.addressLine1 && (
                              <FormErrorMessage fontSize="xs">
                                {facilityErrors[index].addressLine1}
                              </FormErrorMessage>
                            )}
                          </FormControl>

                          {/* Address Line 2 */}
                          <FormControl>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              Address Line 2 (Optional)
                            </FormLabel>
                            <Input
                              value={facility.addressLine2 || ''}
                              onChange={(e) => {
                                const updatedFacilities = [...linkedFacilities];
                                updatedFacilities[index] = { ...updatedFacilities[index], addressLine2: e.target.value };
                                setLinkedFacilities(updatedFacilities);
                              }}
                              placeholder="Enter address line 2"
                              size={{ base: "xs", md: "sm" }}
                              borderRadius="lg"
                            />
                          </FormControl>

                          <Flex gap={{ base: 2, md: 4 }} direction={{ base: "column", md: "row" }}>
                          

                            {/* City */}
                            <FormControl isInvalid={facilityErrors[index]?.city}>
                              <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                                City <Text as="span" color="red.500">*</Text>
                              </FormLabel>
                              <Input
                                value={facility.city}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...updatedFacilities[index], city: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                  
                                  if (facilityErrors[index]?.city) {
                                    setFacilityErrors(prev => ({
                                      ...prev,
                                      [index]: { ...prev[index], city: '' }
                                    }));
                                  }
                                }}
                                placeholder="Enter city"
                                size={{ base: "xs", md: "sm" }}
                                borderRadius="lg"
                              />
                              {facilityErrors[index]?.city && (
                                <FormErrorMessage fontSize="xs">
                                  {facilityErrors[index].city}
                                </FormErrorMessage>
                              )}
                            </FormControl>
                             {/* State */}
                             <FormControl isInvalid={facilityErrors[index]?.state}>
                              <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                                State <Text as="span" color="red.500">*</Text>
                              </FormLabel>
                              <Select
                                value={facility.state}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...updatedFacilities[index], state: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                  
                                  if (facilityErrors[index]?.state) {
                                    setFacilityErrors(prev => ({
                                      ...prev,
                                      [index]: { ...prev[index], state: '' }
                                    }));
                                  }
                                }}
                                placeholder="Select state"
                                size={{ base: "xs", md: "sm" }}
                                borderRadius="lg"
                              >
                                {states.map((state, stateIndex) => (
                                  <option key={stateIndex} value={state.isoCode}>
                                    {state.name}
                                  </option>
                                ))}
                              </Select>
                              {facilityErrors[index]?.state && (
                                <FormErrorMessage fontSize="xs">
                                  {facilityErrors[index].state}
                                </FormErrorMessage>
                              )}
                            </FormControl>
                          </Flex>

                          <Flex gap={{ base: 2, md: 4 }} direction={{ base: "column", md: "row" }}>
                           
                              {/* Pin Code */}
                            <FormControl isInvalid={facilityErrors[index]?.pinCode}>
                              <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                                Pin Code <Text as="span" color="red.500">*</Text>
                              </FormLabel>
                              <Input
                                value={facility.pinCode}
                                onChange={(e) => {
                                 
                                  
                                  const value = e.target.value.replace(/\D/g, '');
                                  
                              
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...updatedFacilities[index], pinCode: value };
                                  setLinkedFacilities(updatedFacilities);
                                  
                                  if (facilityErrors[index]?.pinCode) {
                                    setFacilityErrors(prev => ({
                                      ...prev,
                                      [index]: { ...prev[index], pinCode: '' }
                                    }));
                                  }

                                  if (value.length === 6) {
                                    getExistingFacilityDetailsFromPincode(value, index);
                                  }
                                }}
                                placeholder="Enter pin code"
                                size={{ base: "xs", md: "sm" }}
                                borderRadius="lg"
                                maxLength={6}
                              />
                              {facilityErrors[index]?.pinCode && (
                                <FormErrorMessage fontSize="xs">
                                  {facilityErrors[index].pinCode}
                                </FormErrorMessage>
                              )}
                            </FormControl>
                           

                            {/* Country */}
                            <FormControl isInvalid={facilityErrors[index]?.country}>
                              <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                                Country <Text as="span" color="red.500">*</Text>
                              </FormLabel>
                              <Select
                                value={facility.country}
                                onChange={(e) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...updatedFacilities[index], country: e.target.value };
                                  setLinkedFacilities(updatedFacilities);
                                  
                                  if (facilityErrors[index]?.country) {
                                    setFacilityErrors(prev => ({
                                      ...prev,
                                      [index]: { ...prev[index], country: '' }
                                    }));
                                  }
                                }}
                                size={{ base: "xs", md: "sm" }}
                                borderRadius="lg"
                              >
                                <option value="India">India</option>
                              </Select>
                              {facilityErrors[index]?.country && (
                                <FormErrorMessage fontSize="xs">
                                  {facilityErrors[index].country}
                                </FormErrorMessage>
                              )}
                            </FormControl>
                          </Flex>

                          {/* Amenities */}
                          <FormControl>
                            <FormLabel fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor}>
                              Amenities (Optional)
                            </FormLabel>
                            <Box
                              borderWidth="1px"
                              borderColor="gray.300"
                              borderRadius="lg"
                              bg="white"
                            >
                              <ReactQuill
                                value={facility.amenities || ''}
                                onChange={(content) => {
                                  const updatedFacilities = [...linkedFacilities];
                                  updatedFacilities[index] = { ...updatedFacilities[index], amenities: content };
                                  setLinkedFacilities(updatedFacilities);
                                }}
                                placeholder="Describe available amenities..."
                                theme="snow"
                                modules={{
                                  toolbar: [
                                    [{ 'header': [1, 2, 3, false] }],
                                    ['bold', 'italic', 'underline', 'strike'],
                                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                                    [{ 'color': [] }, { 'background': [] }],
                                    ['link'],
                                    ['clean']
                                  ],
                                }}
                                formats={[
                                  'header', 'bold', 'italic', 'underline', 'strike',
                                  'list', 'bullet', 'color', 'background', 'link'
                                ]}
                                style={{
                                  backgroundColor: 'white',
                                  minHeight: '80px',
                                  fontSize: '14px'
                                }}
                              />
                            </Box>
                          </FormControl>
                        </VStack>
                      ) : (
                        <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                          <Box>
                            <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor} mb={1}>
                              Address:
                            </Text>
                            <Text fontSize={{ base: "sm", md: "md" }} color={textColor}>
                              {facility.addressLine1}
                              {facility.addressLine2 && `, ${facility.addressLine2}`}
                            </Text>
                          </Box>
                          
                          <Box>
                            <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor} mb={1}>
                              Location:
                            </Text>
                            <Text fontSize={{ base: "sm", md: "md" }} color={textColor}>
                              {facility.city}, {states.find(state => state.isoCode === facility.state)?.name || facility.state} - {facility.pinCode}, {facility.country}
                            </Text>
                          </Box>
                          
                          {facility.amenities && facility.amenities.trim() !== '' && facility.amenities !== '<p><br></p>' && facility.amenities !== '<p></p>' && (
                            <Box>
                              <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="semibold" color={labelColor} mb={2}>
                                Amenities:
                              </Text>
                              <Box 
                                p={{ base: 2, md: 3 }}
                                borderRadius="lg"
                                bg={readOnlyBg}
                                borderWidth="1px"
                                borderColor={borderColor}
                                dangerouslySetInnerHTML={{ __html: facility.amenities }}
                                sx={{
                                  "& ol": { paddingLeft: "20px", color: textColor },
                                  "& li": { marginBottom: "4px", color: textColor, fontSize: { base: "xs", md: "sm" } },
                                  "& ul": { paddingLeft: "20px", color: textColor }
                                }}
                              />
                            </Box>
                          )}
                        </VStack>
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </VStack>
          ) : (
            <Card
              bg={readOnlyBg}
              shadow="sm"
              borderRadius="lg"
              border="1px"
              borderColor={borderColor}
              borderStyle="dashed"
            >
              <CardBody p={{ base: 6, md: 8 }}>
                <VStack spacing={{ base: 4, md: 6 }}>
                  
                  <Text color={textColor} fontSize={{ base: "lg", md: "xl" }} fontWeight="medium" textAlign="center">
                    No linked facilities available
                  </Text>
                  {isEditMode ? (
                    <VStack spacing={4}>
                      <Text color={labelColor} fontSize={{ base: "sm", md: "md" }} textAlign="center" maxW="md">
                        Click the button above to add your first facility
                      </Text>
                    </VStack>
                  ) : (
                    <Text color={labelColor} fontSize={{ base: "sm", md: "md" }} textAlign="center" maxW="md">
                      Contact your administrator to link facilities to your academy.
                    </Text>
                  )}
                </VStack>
              </CardBody>
            </Card>
          )}
        </CardBody>
      </Card>
    </ScaleFade>
    </VStack>
  );
};

export default ProfessionalDetailsAcademy; 