import {
  Box,
  Button,
  Card,
  CardBody,
  Checkbox,
  Divider,
  Flex,
  FormControl,
  FormLabel,
  Heading,
  Image,
  Input,
  Menu,
  MenuButton,
  MenuList,
  Text,
  Textarea,
  useToast,
  FormErrorMessage,
  HStack,
  Tag,
} from "@chakra-ui/react";
import React, { useEffect, useState } from "react";
import { FaChevronDown } from "react-icons/fa";
import { useFormik } from "formik";
import axios from "axios";
import { useParams } from "react-router-dom";
import * as Yup from "yup";
import { useSelector } from "react-redux";

const ProfessionalDetails = ({ coachData }) => {
  const [categories, setCategories] = useState([{}]);
  const [awardImages, setAwardImages] = useState([{ image: "" }]);
  const [isLoadingSubmitBtn, setIsLoadingSubmitBtn] = useState(false);
  const [categoryError, setCategoryError] = useState(false);
  const [renderOnSave, setRenderOnSave] = useState(0);
  const [playingExperienceImages, setPlayingExperienceImages] = useState([
    { image: "" },
  ]);
  const [coachingExperienceImages, setCoachingExperienceImages] = useState([
    { image: "" },
  ]);
  const [coachingQualificationsImages, setCoachingQualificationsImages] =
    useState([{ image: "" }]);

  const toast = useToast();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getCategories = async () => {
    try {
      let response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/category`
      );
      setCategories(response.data.data);
    } catch (error) {
      console.log(error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const addQualification = () => {
    formik.setValues({
      ...formik.values,
      coachingQualifications: [
        ...formik.values.coachingQualifications,
        {
          description: "",
          image: "",
        },
      ],
    });
  };

  const addCoachingExperience = () => {
    formik.setValues({
      ...formik.values,
      coachingExperience: [
        ...formik.values.coachingExperience,
        {
          description: "",
          image: "",
        },
      ],
    });
  };

  const addPlayingExperience = () => {
    formik.setValues({
      ...formik.values,
      playerExperience: [
        ...formik.values.playerExperience,
        {
          description: "",
          image: "",
        },
      ],
    });
  };

  const addAwardExperience = () => {
    formik.setValues({
      ...formik.values,
      award: [
        ...formik.values.award,
        {
          description: "",
          image: "",
        },
      ],
    });
  };

  const handleFileChange = async (e, fieldName, index) => {
    try {
      const file = e.currentTarget.files[0];
      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB",
          status: "warning",
          duration: 5000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        toast({
          title: "Image uploaded successfully",
          status: "success",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      } else {
        toast({
          title: "Something went wrong while uploading  image",
          status: "error",
          duration: 3000,
          position: "top",
          isClosable: true,
        });
        e.target.value = null;
      }

      if (fieldName === "award") {
        formik.setFieldValue(`${fieldName}.${index}.image`, url);
        setAwardImages([...awardImages, { image: url }]);
      }
      if (fieldName === "playerExperience") {
        formik.setFieldValue(`${fieldName}.${index}.image`, url);
        setPlayingExperienceImages([
          ...playingExperienceImages,
          { image: url },
        ]);
      }
      if (fieldName === "coachingExperience") {
        formik.setFieldValue(`${fieldName}.${index}.image`, url);
        setCoachingExperienceImages([
          ...coachingExperienceImages,
          { image: url },
        ]);
      }
      if (fieldName === "coachingQualifications") {
        formik.setFieldValue(`${fieldName}.${index}.image`, url);
        setCoachingQualificationsImages([
          ...coachingQualificationsImages,
          { image: url },
        ]);
      }
    } catch (error) {
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url, fieldName, index) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        if (fieldName === "award") {
          await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        }
        if (fieldName === "playerExperience") {
          await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        }
        if (fieldName === "coachingExperience") {
          await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        }
        if (fieldName === "coachingQualifications") {
          await formik.setFieldValue(`${fieldName}.${index}.image`, "");
        }

        const resp = response?.data;
        if (resp) {
          if (url) {
            toast({
              title: "Profile image deleted.",
              status: "success",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong while deleting profile image.",
              status: "error",
              duration: 3000,
              position: "top",
              isClosable: true,
            });
          }
        }
      } catch (error) {
        console.log(error);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    }
  };

  useEffect(() => {
    getCategories();
  }, []);

  const formik = useFormik({
    initialValues: {
      experience: coachData?.experience || "",
      language: coachData?.language || "",
      sportsCategories: coachData?.sportsCategories || [],
      coachingQualifications: coachData?.coachingQualifications?.filter(
        (x) => x.description.length > 0
      ) || [{ description: "", image: "" }],
      coachingExperience: coachData?.coachingExperience || [
        { description: "", image: "" },
      ],
      playerExperience: coachData?.playerExperience || [
        { description: "", image: "" },
      ],
      award: coachData?.award || [{ description: "", image: "" }],
    },
    validationSchema: Yup.object().shape({
      experience: Yup.number("Only Number are allowed")
        .max(99, "can not more than 99 years")
        .required("Experience is required"),
      language: Yup.string().required("At least one language is required"),
      sportsCategories: Yup.array()
        .min(1, "At least one sports category is required")
        .required("Sports categories are required"),
    }),
    onSubmit: (values) => {
      setIsLoadingSubmitBtn(true);
      if (formik.values.sportsCategories.length === 0) {
        setCategoryError(true);
        setIsLoadingSubmitBtn(false);
        return;
      } else {
        let config = {
          method: "patch",
          maxBodyLength: Infinity,
          url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          data: JSON.stringify(values),
        };

        axios
          .request(config)
          .then((response) => {
  
            setIsLoadingSubmitBtn(false);
            toast({
              title: "Coach professional Details updated",
              status: "success",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          })
          .catch((error) => {
            console.log(error);
            setIsLoadingSubmitBtn(false);
            if (error.response.status === 403) {
              toast({
                title: "You don't have an access to perform this action",
                status: "warning",
                duration: 4000,
                position: "top",
                isClosable: true,
              });
            } else {
              toast({
                title: "Something went wrong please try again later",
                status: "error",
                duration: 4000,
                position: "top",
                isClosable: true,
              });
            }
          });
      }
    },
  });

  useEffect(() => {
    setCoachingQualificationsImages(coachData?.coachingQualifications);
    setAwardImages(coachData?.award);
    setPlayingExperienceImages(coachData?.playerExperience);
    setCoachingExperienceImages(coachData?.coachingExperience);
  }, [id, coachData]);


  return (
    <>
      <form>
        <Card
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
              Professional Details
            </Heading>
            <Flex justifyContent={"space-between"} alignItems={"center"} my={4}>
              <FormControl
                flexBasis={"48%"}
                isInvalid={
                  formik.touched.experience && formik.errors.experience
                }
              >
                <FormLabel htmlFor="experience">
                  Experience <Text as="span" color="red.500">*</Text>
                </FormLabel>
                <Input
                  type="number"
                  name="experience"
                  id="experience"
                  autoComplete="none"
                  placeholder="Enter no. of experience"
                  {...formik.getFieldProps("experience")}
                />
                {formik.touched.experience && formik.errors.experience && (
                  <FormErrorMessage>
                    {formik.errors.experience}
                  </FormErrorMessage>
                )}
              </FormControl>
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.language && formik.errors.language}
              >
                <FormLabel htmlFor="language">
                  Language <Text as="span" color="red.500">*</Text>
                </FormLabel>
                <Input
                  type="text"
                  name="language"
                  id="language"
                  autoComplete="none"
                  placeholder="Enter languages separated by commma( , )"
                  {...formik.getFieldProps("language")}
                />
                {formik.touched.language && formik.errors.language && (
                  <FormErrorMessage>{formik.errors.language}</FormErrorMessage>
                )}
              </FormControl>
            </Flex>
            <FormControl isInvalid={formik.touched.sportsCategories && formik.errors.sportsCategories}>
              <FormLabel>
                Sports Category <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Menu>
                <MenuButton
                  as={Button}
                  w={"100%"}
                  rightIcon={<FaChevronDown />}
                >
                  Select Category
                </MenuButton>
                <MenuList minW={window.innerWidth - 600 + "px"}>
                  {categories.map((category, i) => (
                    <Box p={2} key={i}>
                      <Checkbox
                        size="md"
                        id={i}
                        colorScheme="green"
                        value={category} // Set the value to the category itself
                        isChecked={formik.values.sportsCategories.includes(
                          category.name
                        )}
                        onChange={(value) => {
                          if (value.target.checked) {
                            setCategoryError(false);
                            formik.setValues({
                              ...formik.values,
                              sportsCategories: [
                                ...formik.values.sportsCategories,
                                category.name,
                              ],
                            });
                          } else {
                            if (formik.values.sportsCategories.length > 0) {
                              setCategoryError(false);
                            } else {
                              setCategoryError(true);
                            }
                            formik.setValues({
                              ...formik.values,
                              sportsCategories:
                                formik.values.sportsCategories.filter(
                                  (x) => x !== category.name
                                ),
                            });
                          }
                        }}
                      >
                        {category.name}
                      </Checkbox>
                    </Box>
                  ))}
                </MenuList>
              </Menu>
              <HStack spacing={4} mt={2}>
                {formik?.values?.sportsCategories?.map((category,i) => (
                  <Tag
                    size={'md'}
                    key={i}
                    variant="solid"
                    colorScheme="teal"
                  >
                    {category}
                  </Tag>
                ))}
              </HStack>
              {(categoryError || (formik.touched.sportsCategories && formik.errors.sportsCategories)) && (
                <FormErrorMessage>
                  {formik.errors.sportsCategories || "Categories are required"}
                </FormErrorMessage>
              )}
            </FormControl>
            {/* Coaching Qualification */}
            <Card mt={6} bgColor={"gray.50"}>
              <Flex
                justifyContent={"space-between"}
                alignItems={"center"}
                px={4}
                py={4}
              >
                <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
                  Coaching Qualification
                </Heading>
                <Button
                  colorScheme="green"
                  size={"sm"}
                  px={4}
                  type="button"
                  onClick={addQualification}
                >
                  Add
                </Button>
              </Flex>
              {formik.values.coachingQualifications.map(
                (qualification, index) => {
                  return (
                    <Card m={4} key={index}>
                      <CardBody>
                        <FormControl>
                          <Flex
                            justifyContent={"space-between"}
                            alignItems={"center"}
                            mb={4}
                          >
                            <Heading as="h4" size="md" mb={0}>
                              {"Coaching Qualification " + (index + 1)}
                            </Heading>
                            {index !== 0 &&
                              userData?.accessScopes?.coach?.includes(
                                "delete"
                              ) && (
                                <Button
                                  colorScheme="red"
                                  size={"sm"}
                                  px={4}
                                  onClick={() =>
                                    formik.setValues((prevState) => ({
                                      ...prevState,
                                      coachingQualifications:
                                        prevState.coachingQualifications.filter(
                                          (_, i) => i !== index
                                        ),
                                    }))
                                  }
                                >
                                  Remove
                                </Button>
                              )}
                          </Flex>
                          <Divider />
                          <Flex
                            justifyContent={"space-between"}
                            alignItems={"center"}
                          >
                            <Textarea
                              name={`coachingQualifications.${index}.description`}
                              id={`coachingQualifications.${index}.description`}
                              placeholder={"Enter coaching qualification"}
                              {...formik.getFieldProps(
                                `coachingQualifications.${index}.description`
                              )}
                              flexBasis={"60%"}
                            />
                            <Flex
                              flexDirection="column"
                              alignItems="flex-start"
                              flexBasis={"33%"}
                            >
                              {formik?.values?.coachingQualifications[index]
                                ?.image && (
                                <>
                                  <a
                                    href={`${formik?.values?.coachingQualifications[index]?.image}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <Image
                                      src={
                                        formik?.values?.coachingQualifications[
                                          index
                                        ]?.image
                                      }
                                      id={`coachingQualifications.${index}.image`}
                                      alt="Selected Image"
                                      maxW="165px"
                                      maxH="165px"
                                    />
                                  </a>
                                  {userData?.accessScopes?.coach?.includes(
                                    "delete"
                                  ) && (
                                    <Button
                                      mt={2}
                                      colorScheme="red"
                                      size={"sm"}
                                      onClick={(e) => {
                                        e.preventDefault();
                                        formik.setFieldValue(
                                          `coachingQualifications.${index}.image`,
                                          ""
                                        );
                                        setCoachingQualificationsImages(
                                          coachingQualificationsImages.filter(
                                            (x, idx) => idx !== index
                                          )
                                        );
                                        deleteImageFiles(
                                          qualification.image,
                                          "coachingQualifications",
                                          index
                                        );
                                      }}
                                    >
                                      Remove Image
                                    </Button>
                                  )}
                                </>
                              )}
                              {formik.values.coachingQualifications[index]
                                ?.image === "" && (
                                <Box>
                                  <Text as={"span"} fontWeight={"semibold"}>
                                    Add Image
                                  </Text>
                                  <Input
                                    type="file"
                                    id={`coachingQualifications.${index}.image`}
                                    name={`coachingQualifications.${index}.image`}
                                    accept="image/*"
                                    onChange={(e) =>
                                      handleFileChange(
                                        e,
                                        "coachingQualifications",
                                        index
                                      )
                                    }
                                    mt={1}
                                    cursor={"pointer"}
                                  />
                                </Box>
                              )}
                            </Flex>
                          </Flex>
                        </FormControl>
                      </CardBody>
                    </Card>
                  );
                }
              )}
            </Card>

            {/* Coacing Experience */}
            <Card mt={6} bgColor={"gray.50"}>
              <Flex
                justifyContent={"space-between"}
                alignItems={"center"}
                px={4}
                py={4}
              >
                <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
                  Coaching Experience
                </Heading>
                <Button
                  colorScheme="green"
                  size={"sm"}
                  px={4}
                  type="button"
                  onClick={addCoachingExperience}
                >
                  Add
                </Button>
              </Flex>
              {formik.values.coachingExperience.map((experience, index) => {
                return (
                  <Card m={4} key={index}>
                    <CardBody>
                      <FormControl>
                        <Flex
                          justifyContent={"space-between"}
                          alignItems={"center"}
                          mb={4}
                        >
                          <Heading as="h4" size="md" mb={0}>
                            {"Coaching Experience " + (index + 1)}
                          </Heading>
                          {index !== 0 &&
                            userData?.accessScopes?.coach?.includes(
                              "delete"
                            ) && (
                              <Button
                                colorScheme="red"
                                size={"sm"}
                                px={4}
                                onClick={() =>
                                  formik.setValues((prevState) => ({
                                    ...prevState,
                                    coachingExperience:
                                      prevState.coachingExperience.filter(
                                        (_, i) => i !== index
                                      ),
                                  }))
                                }
                              >
                                Remove
                              </Button>
                            )}
                        </Flex>
                        <Divider />
                        <Flex
                          justifyContent={"space-between"}
                          alignItems={"center"}
                        >
                          <Textarea
                            name={`coachingExperience.${index}.description`}
                            id={`coachingExperience.${index}.description`}
                            placeholder={"Enter coaching experience"}
                            {...formik.getFieldProps(
                              `coachingExperience.${index}.description`
                            )}
                            flexBasis={"60%"}
                          />
                          <Flex
                            flexDirection="column"
                            alignItems="flex-start"
                            flexBasis={"33%"}
                          >
                            {formik?.values?.coachingExperience[index]
                              ?.image && (
                              <>
                                <a
                                  href={`${formik?.values?.coachingExperience[index]?.image}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <Image
                                    src={
                                      formik?.values?.coachingExperience[index]
                                        ?.image
                                    }
                                    id={`coachingExperience.${index}.image`}
                                    alt="Selected Image"
                                    maxW="165px"
                                    maxH="165px"
                                  />
                                </a>
                                {userData?.accessScopes?.coach?.includes(
                                  "delete"
                                ) && (
                                  <Button
                                    mt={2}
                                    colorScheme="red"
                                    size={"sm"}
                                    onClick={(e) => {
                                      e.preventDefault();
                                      formik.setFieldValue(
                                        `coachingExperience.${index}.image`,
                                        ""
                                      );
                                      setCoachingExperienceImages(
                                        coachingExperienceImages.filter(
                                          (x, idx) => idx !== index + 1
                                        )
                                      );
                                      deleteImageFiles(
                                        experience.image,
                                        "coachingExperience",
                                        index
                                      );
                                    }}
                                  >
                                    Remove Image
                                  </Button>
                                )}
                              </>
                            )}
                            {formik.values.coachingExperience[index]?.image ===
                              "" && (
                              <>
                                <Text as={"span"} fontWeight={"semibold"}>
                                  Add Image
                                </Text>
                                <Input
                                  type="file"
                                  id={`coachingExperience.${index}.image`}
                                  name={`coachingExperience.${index}.image`}
                                  accept="image/*"
                                  onChange={(e) =>
                                    handleFileChange(
                                      e,
                                      "coachingExperience",
                                      index
                                    )
                                  }
                                  mt={1}
                                  cursor={"pointer"}
                                />
                              </>
                            )}
                          </Flex>
                        </Flex>
                      </FormControl>
                    </CardBody>
                  </Card>
                );
              })}
            </Card>

            {/* Playing Experience */}
            <Card mt={6} bgColor={"gray.50"}>
              <Flex
                justifyContent={"space-between"}
                alignItems={"center"}
                px={4}
                py={4}
              >
                <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
                  Playing Experience
                </Heading>
                <Button
                  colorScheme="green"
                  size={"sm"}
                  px={4}
                  type="button"
                  onClick={addPlayingExperience}
                >
                  Add
                </Button>
              </Flex>
              {formik.values.playerExperience.map((experience, index) => {
                return (
                  <Card m={4} key={index}>
                    <CardBody>
                      <FormControl>
                        <Flex
                          justifyContent={"space-between"}
                          alignItems={"center"}
                          mb={4}
                        >
                          <Heading as="h4" size="md" mb={0}>
                            {"Playing Experience " + (index + 1)}
                          </Heading>
                          {index !== 0 &&
                            userData?.accessScopes?.coach?.includes(
                              "delete"
                            ) && (
                              <Button
                                colorScheme="red"
                                size={"sm"}
                                px={4}
                                onClick={() =>
                                  formik.setValues((prevState) => ({
                                    ...prevState,
                                    playerExperience:
                                      prevState.playerExperience.filter(
                                        (_, i) => i !== index
                                      ),
                                  }))
                                }
                              >
                                Remove
                              </Button>
                            )}
                        </Flex>
                        <Divider />
                        <Flex
                          justifyContent={"space-between"}
                          alignItems={"center"}
                        >
                          <Textarea
                            name={`playerExperience.${index}.description`}
                            id={`playerExperience.${index}.description`}
                            placeholder={"Enter playing experience"}
                            {...formik.getFieldProps(
                              `playerExperience.${index}.description`
                            )}
                            flexBasis={"60%"}
                          />
                          <Flex
                            flexDirection="column"
                            alignItems="flex-start"
                            flexBasis={"33%"}
                          >
                            {formik?.values?.playerExperience[index]?.image && (
                              <>
                                <a
                                  href={`${formik?.values?.playerExperience[index]?.image}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <Image
                                    src={
                                      formik?.values?.playerExperience[index]
                                        ?.image
                                    }
                                    id={`playerExperience.${index}.image`}
                                    alt="Selected Image"
                                    maxW="165px"
                                    maxH="165px"
                                  />
                                </a>
                                {userData?.accessScopes?.coach?.includes(
                                  "delete"
                                ) && (
                                  <Button
                                    mt={2}
                                    colorScheme="red"
                                    size={"sm"}
                                    onClick={(e) => {
                                      e.preventDefault();
                                      formik.setFieldValue(
                                        `playerExperience.${index}.image`,
                                        ""
                                      );
                                      setPlayingExperienceImages(
                                        playingExperienceImages.filter(
                                          (_, idx) => idx !== index + 1
                                        )
                                      );
                                      deleteImageFiles(
                                        experience.image,
                                        "playerExperience",
                                        index
                                      );
                                    }}
                                  >
                                    Remove Image
                                  </Button>
                                )}
                              </>
                            )}
                            {formik.values.playerExperience[index]?.image ===
                              "" && (
                              <>
                                <Text as={"span"} fontWeight={"semibold"}>
                                  Add Image
                                </Text>
                                <Input
                                  type="file"
                                  id={`playerExperience.${index}.image`}
                                  name={`playerExperience.${index}.image`}
                                  accept="image/*"
                                  onChange={(e) =>
                                    handleFileChange(
                                      e,
                                      "playerExperience",
                                      index
                                    )
                                  }
                                  mt={1}
                                  cursor={"pointer"}
                                />
                              </>
                            )}
                          </Flex>
                        </Flex>
                      </FormControl>
                    </CardBody>
                  </Card>
                );
              })}
            </Card>

            {/* Award */}
            <Card mt={6} bgColor={"gray.50"}>
              <Flex
                justifyContent={"space-between"}
                alignItems={"center"}
                px={4}
                py={4}
              >
                <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
                  Award
                </Heading>
                <Button
                  colorScheme="green"
                  size={"sm"}
                  px={4}
                  type="button"
                  onClick={addAwardExperience}
                >
                  Add
                </Button>
              </Flex>
              {formik.values.award.map((award, index) => {
                return (
                  <Card m={4} key={index}>
                    <CardBody>
                      <FormControl>
                        <Flex
                          justifyContent={"space-between"}
                          alignItems={"center"}
                          mb={4}
                        >
                          <Heading as="h4" size="md" mb={0}>
                            {"Award " + (index + 1)}
                          </Heading>
                          {index !== 0 &&
                            userData?.accessScopes?.coach?.includes(
                              "delete"
                            ) && (
                              <Button
                                colorScheme="red"
                                size={"sm"}
                                px={4}
                                onClick={() =>
                                  formik.setValues((prevState) => ({
                                    ...prevState,
                                    award: prevState.award.filter(
                                      (_, i) => i !== index
                                    ),
                                  }))
                                }
                              >
                                Remove
                              </Button>
                            )}
                        </Flex>
                        <Divider />
                        <Flex
                          justifyContent={"space-between"}
                          alignItems={"center"}
                        >
                          <Textarea
                            name={`award.${index}.description`}
                            id={`award.${index}.description`}
                            placeholder={"Enter award details"}
                            {...formik.getFieldProps(
                              `award.${index}.description`
                            )}
                            flexBasis={"60%"}
                          />
                          <Flex
                            flexDirection="column"
                            alignItems="flex-start"
                            flexBasis={"33%"}
                          >
                            {award?.image && (
                              <>
                                <a
                                  href={`${award?.image}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <Image
                                    src={award?.image}
                                    id={`award.${index}.image`}
                                    alt="Selected Image"
                                    maxW="165px"
                                    maxH="165px"
                                  />
                                </a>
                                {userData?.accessScopes?.coach?.includes(
                                  "delete"
                                ) && (
                                  <Button
                                    mt={2}
                                    colorScheme="red"
                                    size={"sm"}
                                    onClick={(e) => {
                                      e.preventDefault();
                                      formik.setFieldValue(
                                        `award.${index}.image`,
                                        ""
                                      );
                                      setAwardImages(
                                        awardImages.filter(
                                          (_, idx) => idx !== index
                                        )
                                      );
                                      deleteImageFiles(
                                        awardImages[index]?.image,
                                        "award",
                                        index
                                      );
                                    }}
                                  >
                                    Remove Image
                                  </Button>
                                )}
                              </>
                            )}
                            {formik.values.award[index]?.image === "" && (
                              <>
                                <Text as={"span"} fontWeight={"semibold"}>
                                  Add Image
                                </Text>
                                <Input
                                  type="file"
                                  id={`award.${index}.image`}
                                  name={`award.${index}.image`}
                                  accept="image/*"
                                  onChange={(e) =>
                                    handleFileChange(e, "award", index)
                                  }
                                  mt={1}
                                  cursor={"pointer"}
                                />
                              </>
                            )}
                          </Flex>
                        </Flex>
                      </FormControl>
                    </CardBody>
                  </Card>
                );
              })}
            </Card>
          </CardBody>
        </Card>
        {userData?.accessScopes?.coach?.includes("write") && (
          <Flex justifyContent={"space-between"} alignItems={"center"} mt={4}>
            <Button
              colorScheme="red"
              flexBasis={"49%"}
              onClick={() => {
                formik.resetForm();
                toast({
                  title: "All Changes has been discarded",
                  status: "success",
                  duration: 4000,
                  position: "top",
                  isClosable: true,
                });
              }}
            >
              Discard
            </Button>
            <Button
              colorScheme="green"
              flexBasis={"49%"}
              type="submit"
              onClick={formik.handleSubmit}
              isLoading={isLoadingSubmitBtn}
            >
              Update
            </Button>
          </Flex>
        )}
      </form>
    </>
  );
};

export default ProfessionalDetails;
