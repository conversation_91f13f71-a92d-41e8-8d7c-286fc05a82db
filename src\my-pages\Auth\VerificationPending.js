import React, { useEffect } from "react";
import {
  Box,
  Button,
  Center,
  Flex,
  Heading,
  Icon,
  Text,
} from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";
import { deleteCookie } from "../../utilities/auth";
import { jwtDecode } from "jwt-decode";

const VerificationPending = () => {
  const navigate = useNavigate();

  // Check authorization status on component mount
  useEffect(() => {
    const checkAuthStatus = () => {
      const token = sessionStorage.getItem("admintoken");
      if (token) {
        try {
          const decodedToken = jwtDecode(token.split(" ")[1]);
          const isAuthorized = decodedToken?.academyId?.authStatus === "authorized";
          
          if (isAuthorized) {
            navigate("/dashboard");
            return;
          }
        } catch (error) {
          console.error("Error decoding token:", error);
          sessionStorage.removeItem("admintoken");
          navigate("/login");
        }
      }
    };

    checkAuthStatus();
  }, [navigate]);

  // Prevent navigation back to login when on verification-pending
  useEffect(() => {
    const handlePopState = (event) => {
      const token = sessionStorage.getItem("admintoken");
      if (token) {
        event.preventDefault();
        navigate("/verification-pending", { replace: true });
      }
    };

    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [navigate]);

 const handleLogout = async () => {
  const token = sessionStorage.getItem("admintoken");
  try {
    const myHeaders = new Headers();
    if (token) {
      myHeaders.append("Authorization", token); // or Bearer token
    }

    const requestOptions = {
      method: "DELETE",
      headers: myHeaders,
      credentials: "include",
    };
    await fetch(`${process.env.REACT_APP_BASE_URL}/api/academy/logout`, requestOptions);
  } catch (error) {
    console.error("Logout error:", error);
  } finally {
    sessionStorage.removeItem("admintoken");
    localStorage.clear();
    deleteCookie("userName");
    navigate("/login");
  }
};

  return (
    <Flex
      minH="100vh"
      direction="column"
      justify="center"
      align="center"
      bg="gray.100"
    >
      <Center flex={1} flexDirection="column">
        <Heading size="lg" mb={4} color="teal.600">
          Academy Verification Under Review
        </Heading>
        <Text fontSize="lg" color="gray.700" mb={6}>
          Thank you for registering! Your details are under review. You will be
          notified once your account is verified.
        </Text>
        <Button
          colorScheme="teal"
          alignSelf="center"
    
          onClick={() => {
            handleLogout();
          }}
        >
          Logout
        </Button>
      </Center>
    </Flex>
  );
};

export default VerificationPending;
