import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Button,
  Card,
  CardBody,
  Flex,
  Heading,
  Spinner,
  Text,
  useToast,
} from "@chakra-ui/react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import axios from "axios";
import { useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";
import { useNavigate } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const DescriptionPageCms = () => {
  const navigate = useNavigate();
  const [descriptionData, setDescriptionData] = useState({
    result: null,
    isLoading: false,
    error: false,
  });
  const [isUpdated, setIsUpdated] = useState(false);
  const [discardBtnLoading, setDiscardBtnLoading] = useState(false);
  const [saveBtnLoading, setSaveBtnLoading] = useState(false);

  const Toast = useToast();
  const token = sessionStorage.getItem("admintoken")?.split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getDescriptionData = () => {
    const decodedToken = jwtDecode(token);
    const academyId = decodedToken.academyId._id;

    setDescriptionData({ result: null, isLoading: true, error: false });
    axios
      .get(`${process.env.REACT_APP_BASE_URL}/api/academy-cms/${academyId}/description`)
      .then((response) => {
        setDescriptionData({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong, please try again later";
        setDescriptionData({ result: null, isLoading: false, error: errorMessage });
        setDiscardBtnLoading(false);
        setIsUpdated(false);
        Toast({
          title: errorMessage,
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      });
  };

  const updateDescriptionData = () => {
    if (!descriptionData.result.description || descriptionData.result.description.length < 10) {
      setSaveBtnLoading(false);
      Toast({
        title: "Please add at least 10 characters in description",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
      return;
    }

    axios
      .post(
        `${process.env.REACT_APP_BASE_URL}/api/academy-cms/description`,
        { description: descriptionData.result.description },
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      )
      .then(() => {
        setSaveBtnLoading(false);
        setIsUpdated(false);
        Toast({
          title: "Description updated successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch(() => {
        setSaveBtnLoading(false);
        Toast({
          title: "Something went wrong, please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      });
  };

  useEffect(() => {
    getDescriptionData();
  }, []);

  return (
    <Box bgColor="#f2f2f2" minH="100vh">
      <Layout title="CMS | Description">
        {/* Responsive Back Button, Breadcrumb, and Action Buttons */}
        <Flex direction={{ base: "column", md: "row" }} alignItems={{ base: "flex-start", md: "center" }} justifyContent={{ base: "flex-start", md: "space-between" }} gap={{ base: 2, md: 0 }} pb={4}>
          <Flex alignItems="center" gap={0} mb={{ base: 3, md: 0 }}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize={{ base: "xs", sm: "sm" }} pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Description</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Flex gap={2} direction={{ base: "row", sm: "row" }} w={{ base: "full", md: "auto" }}>
              <Button
                variant="outline"
                colorScheme="red"
                size={{ base: "xs", md: "sm" }}
                w={{ base: "full", sm: "auto" }}
                isDisabled={!isUpdated}
                isLoading={discardBtnLoading}
                onClick={() => {
                  setDiscardBtnLoading(true);
                  getDescriptionData();
                }}
              >
                Discard
              </Button>
              <Button
                variant="solid"
                colorScheme="green"
                size={{ base: "xs", md: "sm" }}
                w={{ base: "full", sm: "auto" }}
                isDisabled={!isUpdated}
                isLoading={saveBtnLoading}
                onClick={() => {
                  setSaveBtnLoading(true);
                  updateDescriptionData();
                }}
              >
                Save Changes
              </Button>
            </Flex>
          )}
        </Flex>

        {/* Content Area */}
        {descriptionData.isLoading ? (
          <Flex justifyContent="center" alignItems="center" mt={12}>
            <Spinner size="lg" />
          </Flex>
        ) : descriptionData.error ? (
          <Flex justifyContent="center" alignItems="center" mt={12}>
            <Text color="red.500" textAlign="center" fontSize={{ base: "sm", md: "md" }}>
              {descriptionData?.error}
            </Text>
          </Flex>
        ) : (
          (descriptionData.result && descriptionData.result.description) ? (
            <Card mt={4}>
              <CardBody p={{ base: 4, md: 6 }}>
                <Heading as="h4" size={{ base: "sm", md: "md" }} mb={3}>
                  Academy Description
                </Heading>
                <Box>
                  <ReactQuill
                    theme="snow"
                    value={descriptionData.result.description || ""}
                    onChange={(value) => {
                      setDescriptionData((prev) => ({
                        ...prev,
                        result: { ...prev.result, description: value },
                      }));
                      setIsUpdated(true);
                    }}
                    readOnly={!userData?.accessScopes?.cms?.includes("write")}
                    modules={!userData?.accessScopes?.cms?.includes("write") ? { toolbar: false } : {}}
                    style={{
                      minHeight: "200px",
                      fontSize: "14px",
                      borderRadius: "8px",
                    }}
                  />
                </Box>
              </CardBody>
            </Card>
          ) : (
            <Card mt={4}>
              <CardBody p={{ base: 4, md: 6 }}>
                <Heading as="h4" size={{ base: "sm", md: "md" }} mb={3}>
                  Add Academy Description
                </Heading>
                <Box>
                  <ReactQuill
                    theme="snow"
                    value={descriptionData.result?.description || ""}
                    onChange={(value) => {
                      setDescriptionData((prev) => ({
                        ...prev,
                        result: { ...(prev.result || {}), description: value },
                      }));
                    }}
                    readOnly={!userData?.accessScopes?.cms?.includes("write")}
                    modules={!userData?.accessScopes?.cms?.includes("write") ? { toolbar: false } : {}}
                    style={{
                      minHeight: "200px",
                      fontSize: "14px",
                      borderRadius: "8px",
                    }}
                  />
                  <Button
                    mt={4}
                    colorScheme="telegram"
                    size="md"
                    isLoading={saveBtnLoading}
                    isDisabled={!(descriptionData.result?.description && descriptionData.result.description.length >= 10)}
                    onClick={() => {
                      setSaveBtnLoading(true);
                      updateDescriptionData();
                    }}
                  >
                    Add Description
                  </Button>
                </Box>
              </CardBody>
            </Card>
          )
        )}
      </Layout>
    </Box>
  );
};

export default DescriptionPageCms;
