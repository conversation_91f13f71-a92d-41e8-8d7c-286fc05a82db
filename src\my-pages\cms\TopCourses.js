import React, { useEffect, useState } from "react";
import { useMediaQuery } from "@chakra-ui/react";
import Layout from "../../layout/default";
import { MdDelete, MdDragHandle } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Input,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Button,
  Stack,
  Text,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  TableContainer,
  ModalFooter,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Badge,
  Tooltip,
  InputGroup,
  InputRightAddon,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import { useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { IoIosClose } from "react-icons/io";
import debounce from "../../utilities/debounce";

const TopCourses = () => {
  const navigate = useNavigate();
  const [courseData, setCourseData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [cmsCourseData, setCmsCourseData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevCourseData, setPrevCourseData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleted, setIsDeleted] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteCourseId, setDeleteCourseId] = useState(null);
  const [deleteBtnLoading, setDeleteBtnLoading] = useState(false);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);
  const [isMobile] = useMediaQuery("(max-width: 768px)");

  const debouncedGetCourseData = React.useCallback(
    debounce((query) => getCourseData(query), 400),
    []
  );

  const getCourseData = (searchQuery) => {
    setCourseData({ result: [], isLoading: true, error: false });
    let url = `${process.env.REACT_APP_BASE_URL}/api/course?page=1`;
    if (searchQuery && searchQuery.length > 0) {
      url = `${process.env.REACT_APP_BASE_URL}/api/course?page=1&courseName=${encodeURIComponent(searchQuery)}`;
    }
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: url,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setCourseData({
          result: response.data.data,
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setCourseData({ result: [], isLoading: false, error: errorMessage });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: errorMessage,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const getCmsCourseData = () => {
    setCmsCourseData({ result: [], isLoading: true, error: false });
    
    // Decode token to get academyId
    const decodedToken = jwtDecode(token);
    const academyId = decodedToken.academyId._id;
    
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/${academyId}/course`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setCmsCourseData({
          result: response.data.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setCmsCourseData({ result: [], isLoading: false, error: errorMessage });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: errorMessage,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAndSave = (customCourseList = null) => {
    onClose1();
    setSaveChangesBtnLoading(true);
    const courseList = customCourseList || cmsCourseData.result;
    const getIds = courseList.map((id, index) => {
      return { course: id?.course?._id, position: index + 1 };
    });

    let data = JSON.stringify({
      documents: getIds,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/course/add`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        getCmsCourseData();
        getCourseData(searchQuery);
        setSaveChangesBtnLoading(false);
        setIsUpdated(false);
        onClose();
        setIsDeleted(false);
        toast({
          title: "Training Schedule updated successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setSaveChangesBtnLoading(false);
        if (
          error.response?.data?.errors && error.response.data.errors.length > 0
        ) {
          toast({
            title: error.response.data.errors[0].message,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (
          error.response.data.message === "Top Course cannot be greater than 15"
        ) {
          getCmsCourseData();
          setIsUpdated(false);
          toast({
            title: error.response.data.message,
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...cmsCourseData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setCmsCourseData({ ...cmsCourseData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = cmsCourseData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/course/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevCourseData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPrevCourseData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getCmsCourseData();
  }, []);

  // Delete Course Modal logic
  const handleDeleteCourse = (courseId) => {
    setDeleteCourseId(courseId);
    setIsDeleteModalOpen(true);
  };

  const confirmDeleteCourse = () => {
    setDeleteBtnLoading(true);
    // Remove the course from the list
    const updatedCourseList = cmsCourseData.result.filter(
      (item) => item?.course?._id !== deleteCourseId
    );
    
    // Update state and call save API
    setCmsCourseData({
      ...cmsCourseData,
      result: updatedCourseList,
    });
    setIsUpdated(true);
    updateAndSave(updatedCourseList);
    setIsDeleteModalOpen(false);
    setDeleteBtnLoading(false);
  };

  const handleSearchInputChange = (e) => {
    setSearchQuery(e.target.value);
    debouncedGetCourseData(e.target.value);
  };

  return (
    <Layout title="CMS | Training Schedules">
      {/* Mobile Heading and Buttons in one row */}
      {isMobile ? (
        <Flex direction="column" alignItems="flex-start" gap={1} mb={3}>
          <Flex alignItems="center" gap={0} mb={{base: 4, md: 0}} pb={{base: 2, md: 0}}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize={{base: "md", md: "sm"}} pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Training Schedules</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && (
            <Flex direction="row" gap={2} w="100%" justifyContent="flex-end">
              <Button
                variant={"outline"}
                colorScheme="telegram"
                size="xs"
                py={4}
                px={2}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  setAdjustBtnEdit(true);
                  setPrevCourseData(cmsCourseData.result);
                  getCmsCourseData();
                  setIsUpdated(false);
                }}
                mr={1}
                mb={0}
                isDisabled={adjustBtnEdit || cmsCourseData.result.length <= 1}
              >
                Adjust Position
              </Button>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size="xs"
                py={4}
                px={2}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  onOpen();
                  setSearchQuery("");
                  getCourseData("");
                }}
                mr={0}
                mb={0}
                isDisabled={!cmsCourseData?.isLoading && cmsCourseData?.error}
              >
                Add Training Schedule
              </Button>
            </Flex>
          )}
        </Flex>
      ) : (
        <Flex justifyContent={{ base: "flex-start", md: "space-between" }} alignItems={{ base: "flex-start", md: "center" }} direction={{ base: "column", md: "row" }} wrap="wrap">
          {/* Hide Breadcrumb on mobile */}
            <Breadcrumb fontWeight="medium" fontSize={{ base: "xs", md: "sm" }}>
            <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
              <Button
                variant="ghost"
                size={{ base: "xs", md: "sm" }}
                leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
                onClick={() => navigate(-1)}
                _hover={{ bg: "gray.100" }}
                color="gray.700"
                fontWeight="bold"
                className="p-0"
              >
              </Button>
            </Flex>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink>CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Training Schedules</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>

          {/* Responsive button group */}
          <Box mt={{ base: 2, md: 0 }} w={{ base: "100%", md: "auto" }}>
            {userData?.accessScopes?.cms?.includes("write") && (
              <>
                {isDeleted ? (
                  !(!cmsCourseData?.isLoading && cmsCourseData?.error) &&
                  !adjustBtnEdit &&
                  userData?.accessScopes?.cms?.includes("write") && (
                    <Flex direction={{ base: "column", md: "row" }} justifyContent={{ base: "flex-start", md: "flex-end" }} alignItems={{ base: "flex-start", md: "center" }} wrap="wrap">
                      <Button
                        variant={"outline"}
                        colorScheme="red"
                        size={{ base: "xs", md: "sm" }}
                        py={{ base: 2, md: 5 }}
                        px={0}
                        mr={{ base: 0, md: 4 }}
                        mb={{ base: 2, md: 0 }}
                        isDisabled={!isUpdated}
                        w={{ base: "100%", md: "auto" }}
                        onClick={() => {
                          setIsDeleted(false);
                          getCmsCourseData();
                          setIsUpdated(false);
                        }}
                      >
                        Discard
                      </Button>
                      <Button
                        variant={"outline"}
                        colorScheme="green"
                        size={{ base: "xs", md: "sm" }}
                        py={{ base: 2, md: 5 }}
                        px={0}
                        isDisabled={!isUpdated}
                        w={{ base: "100%", md: "auto" }}
                        onClick={onOpen1}
                        isLoading={saveChangesBtnLoading}
                      >
                        Save Changes
                      </Button>
                    </Flex>
                  )
                ) : (
                  <Flex wrap="wrap">
                    {!adjustBtnEdit ? (
                      <Box w={{ base: "100%", md: "auto" }}>

                        <Button
                          variant={"outline"}
                          colorScheme="telegram"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={0}
                          mr={{ base: 0, md: 3 }}
                          mb={{ base: 2, md: 0 }}
                          w={{ base: "100%", md: "auto" }}
                          onClick={() => {
                            setAdjustBtnEdit(true);
                            setPrevCourseData(cmsCourseData.result);
                            getCmsCourseData();
                            setIsUpdated(false);
                          }}
                          isDisabled={cmsCourseData.result.length <= 1}
                        >
                          Adjust Position
                        </Button>
                        <Button
                          variant={"outline"}
                          colorScheme="teal"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={0}
                          w={{ base: "100%", md: "auto" }}
                          onClick={() => {
                            onOpen();
                            setSearchQuery("");
                            getCourseData("");
                          }}
                          mr={{ base: 0, md: 2 }}
                          isDisabled={
                            !cmsCourseData?.isLoading && cmsCourseData?.error
                          }
                        >
                          Add Training Schedule
                        </Button>
                      </Box>
                    ) : (
                      <Flex direction={{ base: "column", md: "row" }} wrap="wrap" w={{ base: "100%", md: "auto" }}>
                        <Button
                          variant={"outline"}
                          colorScheme="red"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={0}
                          mr={{ base: 0, md: 4 }}
                          mb={{ base: 2, md: 0 }}
                          w={{ base: "100%", md: "auto" }}
                          onClick={() => {
                            setCmsCourseData({
                              result: prevCourseData,
                              isLoading: false,
                              error: false,
                            });
                            setAdjustBtnEdit(false);
                            getCmsCourseData();
                            setIsUpdated(false);
                          }}
                        >
                          Discard
                        </Button>
                        <Button
                          variant={"outline"}
                          colorScheme="green"
                          size={{ base: "xs", md: "sm" }}
                          py={{ base: 2, md: 5 }}
                          px={0}
                          isLoading={posBtnLoading}
                          w={{ base: "100%", md: "auto" }}
                          onClick={updateBlockPosition}
                        >
                          Save Changes
                        </Button>
                      </Flex>
                    )}
                  </Flex>
                )}
              </>
            )}
          </Box>
        </Flex>
      )}
      {/* Added/Selected Training Schedule List: Card view on mobile, table on desktop */}
      {isMobile ? (
        !cmsCourseData?.isLoading && cmsCourseData?.error ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={10}>
            <Text color="red.500">{cmsCourseData?.error}</Text>
          </Flex>
        ) : cmsCourseData?.isLoading ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={6}>
            <Spinner size="md" />
          </Flex>
        ) : cmsCourseData?.result?.length === 0 ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={10}>
            <Text color="gray.500" fontSize="md" fontWeight="semibold">No data to show</Text>
          </Flex>
        ) : (
          <Box mt={2}>
            {cmsCourseData?.result?.map((courseData, inx) => (
              <Box
                key={courseData?._id || inx}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
                p={4}
                mb={3}
                bg="white"
              >
                {/* Training Schedule Name */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Training Schedule Name:</Text>
                  <Text fontSize="sm">{courseData?.course?.courseName || "n/a"}</Text>
                </Flex>
                {/* Coach Name */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Coach Name:</Text>
                  <Text fontSize="sm">{courseData?.course?.coachName || "n/a"}</Text>
                </Flex>
                {/* Status */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Status:</Text>
                  <Badge
                    fontSize="0.8em"
                    px={2}
                    borderRadius="md"
                    bg={courseData?.course?.status === "active" ? "#38A169" : "#E53E3E"}
                    color="white"
                  >
                    {courseData?.course?.status?.toUpperCase()}
                  </Badge>
                </Flex>
                {/* Category */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Category:</Text>
                  <Text fontSize="sm">{courseData?.course?.category || "N/A"}</Text>
                </Flex>
                {/* Proficiency */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Proficiency:</Text>
                  <Text fontSize="sm">{courseData?.course?.proficiency?.[0]?.charAt(0).toUpperCase() + courseData?.course?.proficiency?.[0]?.slice(1) || "N/A"}</Text>
                </Flex>
                {/* Enrolled */}
                <Flex justify="space-between" align="center">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Enrolled:</Text>
                  <Text fontSize="sm">{courseData?.course?.playerEnrolled || "N/A"}</Text>
                </Flex>
                <Flex justifyContent="flex-end" alignItems="center" mt={2}>
                  {!adjustBtnEdit ? (
                    userData?.accessScopes?.cms?.includes("delete") && (
                      <Flex
                        justifyContent="flex-end"
                        alignItems="center"
                        cursor="pointer"
                        onClick={() => handleDeleteCourse(courseData?.course?._id)}
                      >
                        <Text>
                          <MdDelete fontSize="18px" />
                        </Text>
                      </Flex>
                    )
                  ) : (
                    <Text as="span" ml={2}>
                      <MdDragHandle style={{ cursor: "grab" }} fontSize="24px" />
                    </Text>
                  )}
                </Flex>
              </Box>
            ))}
          </Box>
        )
      ) : (
        !cmsCourseData?.isLoading && cmsCourseData?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              {cmsCourseData?.error}
            </Text>
          </Flex>
        ) : (
          <TableContainer
            mt={{ base: 2, md: 6 }}
            height={{ base: "auto", md: `${window.innerHeight - 200}px` }}
            overflowY={{ base: "visible", md: "scroll" }}
            px={{ base: 1, md: 0 }}
          >
            <Table variant="simple" size={{ base: "sm", md: "md" }}>
              <Thead
                bgColor={"#c1eaee"}
                position={{ base: "static", md: "sticky" }}
                top={{ base: "auto", md: "0px" }}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Position</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Training Schedule Name</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Coach Name</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Status</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Category</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Proficiency</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Enrolled</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }} textAlign={"center"}>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {cmsCourseData?.isLoading && !cmsCourseData?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td> </Td>
                    <Td display={{ base: "block", md: "flex" }} justifyContent={{ base: "center", md: "flex-end" }} alignItems={{ base: "center", md: "center" }}>
                      <Spinner />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : cmsCourseData?.result?.length === 0 ? (
                  <Tr>
                    <Td colSpan={8} textAlign="center" py={10}>
                      <Text color="gray.500" fontSize="md" fontWeight="semibold">No data to show</Text>
                    </Td>
                  </Tr>
                ) : (
                  cmsCourseData?.result?.map((courseData, inx) => {
                    return (
                      <Tr
                        key={inx}
                        draggable={adjustBtnEdit}
                        onDragStart={adjustBtnEdit ? () => handleDragStart(inx) : undefined}
                        onDragOver={adjustBtnEdit ? () => handleDragOver(inx) : undefined}
                        onDragEnd={adjustBtnEdit ? handleDragEnd : undefined}
                      >
                        <Td fontSize={{ base: "xs", md: "14px" }}>{inx + 1 + "."}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }} style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }}>{courseData?.course?.courseName}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }} style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }}>{courseData?.course?.coachName}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>
                          <Badge colorScheme={courseData?.course?.status === "active" ? "green" : "red"}>
                            {courseData?.course?.status?.toUpperCase()}
                          </Badge>
                        </Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{courseData?.course?.category || "N/A"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{courseData?.course?.proficiency?.[0]?.charAt(0).toUpperCase() + courseData?.course?.proficiency?.[0]?.slice(1) || "N/A"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{courseData?.course?.playerEnrolled || "N/A"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>
                          {!adjustBtnEdit ? (
                            userData?.accessScopes?.cms?.includes("delete") && (
                              <Flex justifyContent={{ base: "flex-start", md: "center" }} alignItems={{ base: "flex-start", md: "center" }} cursor="pointer" onClick={() => handleDeleteCourse(courseData?.course?._id)}>
                                <Tooltip label="Delete Training Schedule">
                                  <Text>
                                    <MdDelete fontSize={{ base: "18px", md: "24px" }} />
                                  </Text>
                                </Tooltip>
                              </Flex>
                            )
                          ) : (
                            <Flex justifyContent={{ base: "flex-start", md: "center" }} alignItems={{ base: "flex-start", md: "center" }}>
                              <Text as={"span"} ml={3}>
                                <MdDragHandle style={{ cursor: "grab" }} fontSize={{ base: "18px", md: "24px" }} />
                              </Text>
                            </Flex>
                          )}
                        </Td>
                      </Tr>
                    );
                  })
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )
      )}
      {/* Modal for training schedule search */}
      <Modal isOpen={isOpen} onClose={() => { onClose(); getCmsCourseData(); setIsUpdated(false); }} size={{ base: "sm", md: "5xl" }}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader px={{ base: 2, md: 6 }} py={2} position="relative">
            <Box position="absolute" top={0} right={2} zIndex={1}>
              <ModalCloseButton />
            </Box>
              <Flex direction="row" alignItems="center" justifyContent="space-between" gap={2} mt={7} mr={4} ml={4}>
              <Text flex={1} fontSize={{ base: "md", md: "xl" }} fontWeight="bold" mb={0}>
                Search Training Schedule
              </Text>
              {!(!cmsCourseData?.isLoading && cmsCourseData?.error) &&
                !adjustBtnEdit &&
                userData?.accessScopes?.cms?.includes("write") && (
                  <Flex gap={1} flex={2} justifyContent="flex-end">
                    <Button
                      variant={"outline"}
                      colorScheme="red"
                      size={{ base: "xs", md: "sm" }}
                      py={{ base: 1, md: 3 }}
                      px={{ base: 2, md: 4 }}
                      mr={0}
                      isDisabled={!isUpdated}
                      fontSize={{ base: "xs", md: "sm" }}
                      onClick={() => {
                        getCmsCourseData();
                        setIsUpdated(false);
                        onClose();
                      }}
                      w={{ base: "auto", md: "auto" }}
                    >
                      Discard
                    </Button>
                    <Button
                      variant={"outline"}
                      colorScheme="green"
                      size={{ base: "xs", md: "sm" }}
                      py={{ base: 1, md: 3 }}
                      px={{ base: 2, md: 4 }}
                      isDisabled={!isUpdated}
                      fontSize={{ base: "xs", md: "sm" }}
                      onClick={onOpen1}
                      isLoading={saveChangesBtnLoading}
                      w={{ base: "auto", md: "auto" }}
                    >
                      Save Changes
                    </Button>
                  </Flex>
                )}
            </Flex>
          </ModalHeader>
          <ModalBody>
            <Box mt={3} height={"95%"}>
              <Box>
                <Box>
                  <Stack direction="row" spacing={4} align="center">
                    <InputGroup>
                      <Input
                        type="text"
                        placeholder="Search Training Schedule"
                        value={searchQuery}
                        onChange={handleSearchInputChange}
                        onKeyDown={(e) => e.key === "Enter" && getCourseData(searchQuery)}
                      />
                      {searchQuery && (
                        <InputRightAddon
                          as="button"
                          onClick={() => {
                            setSearchQuery("");
                            getCourseData("");
                          }}
                          cursor="pointer"
                          bg="transparent"
                          border="none"
                        >
                          <IoIosClose fontSize="20px" />
                        </InputRightAddon>
                      )}
                    </InputGroup>
                    <Button
                      variant="solid"
                      colorScheme="telegram"
                      onClick={() => getCourseData(searchQuery)}
                      isDisabled={!(searchQuery.length >= 3)}
                    >
                      Search
                    </Button>
                  </Stack>
                  {/* Searched Training Schedule List */}
                  {!courseData?.isLoading && courseData?.error ? (
                    <Flex justifyContent={"center"} alignItems={"center"} w={"full"} my={10}>
                      <Text color={"red.500"}>{courseData?.error}</Text>
                    </Flex>
                  ) : isMobile ? (
                    courseData.result.length > 0 ? (
                      <Box mt={4}>
                        <Flex direction="column" gap={3}>
                          {courseData.result.map((course, inx) => (
                            <Box key={course?._id || inx} borderWidth="1px" borderRadius="md" boxShadow="sm" p={3} bg="white">
                              <Flex justifyContent="space-between" alignItems="center" mb={1}>
                                <Text fontWeight="bold" fontSize="sm">{course?.courseName || "n/a"}</Text>
                                <Text color="gray.400" fontSize="xs">#{inx + 1}</Text>
                              </Flex>
                              <Flex alignItems="center" justifyContent="space-between" mb={1}>
                                <Text fontSize="xs" color="gray.600" fontWeight="semibold">Coach</Text>
                                <Text fontSize="xs" color="gray.700" fontWeight="normal" maxW="60%" textAlign="right" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">{course?.coachName || "n/a"}</Text>
                              </Flex>
                              <Flex alignItems="center" justifyContent="space-between" mb={1}>
                                <Text fontSize="xs" color="gray.600" fontWeight="semibold">Status</Text>
                                <Badge colorScheme={course?.status === "active" ? "green" : "red"} fontSize="0.7em">{course?.status?.toUpperCase()}</Badge>
                              </Flex>
                              <Flex alignItems="center" justifyContent="space-between" mb={1}>
                                <Text fontSize="xs" color="gray.600" fontWeight="semibold">Category</Text>
                                <Text fontSize="xs" color="gray.700" fontWeight="normal" maxW="60%" textAlign="right" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">{course?.category || "N/A"}</Text>
                              </Flex>
                              <Flex alignItems="center" justifyContent="space-between" mb={1}>
                                <Text fontSize="xs" color="gray.600" fontWeight="semibold">Proficiency</Text>
                                <Text fontSize="xs" color="gray.700" fontWeight="normal" maxW="60%" textAlign="right" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">{course?.proficiency?.[0]?.charAt(0).toUpperCase() + course?.proficiency?.[0]?.slice(1) || "N/A"}</Text>
                              </Flex>
                              <Flex alignItems="center" justifyContent="space-between" mb={1}>
                                <Text fontSize="xs" color="gray.600" fontWeight="semibold">Enrolled</Text>
                                <Text fontSize="xs" color="gray.700" fontWeight="normal" maxW="60%" textAlign="right" whiteSpace="nowrap" overflow="hidden" textOverflow="ellipsis">{course?.playerEnrolled || "N/A"}</Text>
                              </Flex>
                              <Flex justifyContent="flex-end" alignItems="center" mt={2}>
                                <Flex justifyContent="flex-end" w="35%">
                                  {cmsCourseData?.result.every((z) => z?.course?._id !== course?._id) ? (
                                    <Button
                                      colorScheme="telegram"
                                      isDisabled={course?.status !== "active"}
                                      size="xs"
                                      fontSize="xs"
                                      onClick={() => {
                                        setIsUpdated(true);
                                        setCmsCourseData((prevState) => ({
                                          ...prevState,
                                          result: [
                                            ...prevState.result,
                                            { course: course },
                                          ],
                                        }));
                                      }}
                                      w="100%"
                                    >
                                      Add
                                    </Button>
                                  ) : (
                                    <Button
                                      colorScheme="red"
                                      size="xs"
                                      fontSize="xs"
                                      onClick={() => {
                                        setIsUpdated(true);
                                        setCmsCourseData((prevState) => ({
                                          ...prevState,
                                          result: prevState.result.filter(
                                            (obj) => obj?.course?._id !== course?._id
                                          ),
                                        }));
                                      }}
                                      w="100%"
                                    >
                                      Remove
                                    </Button>
                                  )}
                                </Flex>
                              </Flex>
                            </Box>
                          ))}
                        </Flex>
                      </Box>
                    ) : courseData?.isLoading && !courseData?.error ? (
                      <Flex justifyContent="center" alignItems="center" mt={6}>
                        <Spinner size="lg" />
                      </Flex>
                    ) : (
                      <Flex justifyContent="center" alignItems="center" mt={6}>
                        <Text fontSize="sm" fontWeight="semibold" color="red.300">
                          {searchQuery ? "Result not found" : "Search for training schedules to add"}
                        </Text>
                      </Flex>
                    )
                  ) : (
                    <TableContainer mt={6} height={`${window.innerHeight - 300}px`} overflowY={"scroll"}>
                      <Table variant="simple">
                        <Thead bgColor={"#c1eaee"} position={"sticky"} top={"0px"} zIndex={"99"}>
                          <Tr bgColor={"#E2DFDF"}>
                            <Th>S.No</Th>
                            <Th>Training Schedule Name</Th>
                            <Th>Coach Name</Th>
                            <Th>Status</Th>
                            <Th>Category</Th>
                            <Th>Proficiency</Th>
                            <Th>Enrolled</Th>
                            <Th textAlign={"center"}>Action</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {courseData?.isLoading && !courseData?.error ? (
                            <Tr>
                              <Td></Td>
                              <Td></Td>
                              <Td> </Td>
                              <Td display={"flex"} justifyContent={"flex-end"} alignItems={"center"}>
                                <Spinner />
                              </Td>
                              <Td></Td>
                              <Td></Td>
                              <Td></Td>
                              <Td></Td>
                            </Tr>
                          ) : courseData?.result?.length === 0 ? (
                            <Tr>
                              <Td colSpan={8} textAlign="center" py={10}>
                                <Text fontSize="sm" fontWeight="semibold" color="red.300">
                                  {searchQuery ? "Result not found" : "Search for training schedules to add"}
                                </Text>
                              </Td>
                            </Tr>
                          ) : (
                            courseData?.result?.map((courseData, inx) => (
                              <Tr key={inx}>
                                <Td>{inx + 1 + "."}</Td>
                                <Td style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }}>{courseData?.courseName}</Td>
                                <Td style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }}>{courseData?.coachName}</Td>
                                <Td>
                                  <Badge colorScheme={courseData?.status === "active" ? "green" : "red"}>{courseData?.status?.toUpperCase()}</Badge>
                                </Td>
                                <Td>{courseData?.category || "N/A"}</Td>
                                <Td>{courseData?.proficiency?.[0]?.charAt(0).toUpperCase() + courseData?.proficiency?.[0]?.slice(1) || "N/A"}</Td>
                                <Td>{courseData?.playerEnrolled || "N/A"}</Td>
                                <Td>
                                  {cmsCourseData?.result.every((z) => z?.course?._id !== courseData?._id) ? (
                                    <Button colorScheme="telegram" size={"sm"} onClick={() => {
                                      setIsUpdated(true);
                                      setCmsCourseData((prevState) => ({
                                        ...prevState,
                                        result: [
                                          ...prevState.result,
                                          { course: courseData },
                                        ],
                                      }));
                                    }}>
                                      Add
                                    </Button>
                                  ) : (
                                    <Button colorScheme="red" size={"sm"} onClick={() => {
                                      setIsUpdated(true);
                                      setCmsCourseData((prevState) => ({
                                        ...prevState,
                                        result: prevState.result.filter((obj) => obj?.course?._id !== courseData?._id),
                                      }));
                                    }}>
                                      Remove
                                    </Button>
                                  )}
                                </Td>
                              </Tr>
                            ))
                          )}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  )}
                </Box>
              </Box>
            </Box>
          </ModalBody>
        </ModalContent>
      </Modal>
      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        size={{ base: "sm", md: "xl" }}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={onClose1}>No</Button>
            <Button
              colorScheme="telegram"
              ml={3}
              onClick={() => updateAndSave()}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {/* Delete Course Confirmation Modal */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={() => setIsDeleteModalOpen(false)}
        isOpen={isDeleteModalOpen}
        isCentered
      >
        <AlertDialogOverlay />
        <AlertDialogContent maxW={{ base: "90vw", md: "400px" }}>
          <AlertDialogHeader>Delete Training Schedule</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to remove this training schedule from Training Schedules?
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button onClick={() => setIsDeleteModalOpen(false)} mr={3}>
              Cancel
            </Button>
            <Button 
              colorScheme="red" 
              isLoading={deleteBtnLoading} 
              onClick={confirmDeleteCourse}
            >
              Confirm
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default TopCourses;
