import {
  Box,
  Button,
  Card,
  CardBody,
  FormControl,
  FormLabel,
  Heading,
  Image,
  Input,
  useToast,
  Flex,
  Select,
  FormErrorMessage,
  RadioGroup,
  Radio,
  HStack,
  Text,
} from "@chakra-ui/react";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { Link, useParams } from "react-router-dom";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";
import { Country, State, City } from "country-state-city";

const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

const KYCDetailsCoach = ({ coachData }) => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImagesError, setSelectedImagesError] = useState(false);
  const [isLoadingSubmitBtn, setIsLoadingSubmitBtn] = useState(false);
  const toast = useToast();
  const { id } = useParams();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);
  const [states, setStates] = useState([]);
  const countryCode = "IN";
  useEffect(() => {
    const stateData = State.getStatesOfCountry("IN");
    setStates(stateData);
  }, []);
  const handleFileChange = async (e, index) => {
    try {
      const file = e.currentTarget.files[0];

      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        toast({
          title: "Image Uploaded Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title:
            "Something went wrong while uploading image, please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }

      setSelectedImages([...selectedImages, { url: url }]);
      formik.setFieldValue(`kycDocuments.documentImg`, [
        ...selectedImages,
        { url: url },
      ]);
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const deleteImageFiles = async (url, index) => {
    if (!userData?.accessScopes?.coach?.includes("delete")) {
      toast({
        title: "You don't have an access to perform this action",
        status: "warning",
        duration: 4000,
        position: "top",
        isClosable: true,
      });
    } else {
      try {
        const formData = new FormData();
        formData.append("url", url);

        const response = await axios.post(
          `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const resp = response?.data;
        formik.setFieldValue(`kycDocuments.documentImg.${index}.url`, "");
        toast({
          title: "Image removed Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } catch (error) {
        console.log(error);
        if (error?.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      }
    }
  };

  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;

  // Add separate state for Aadhaar images
  const [selectedAadhaarImages, setSelectedAadhaarImages] = useState([]);
  const [selectedAadhaarImagesError, setSelectedAadhaarImagesError] = useState(false);

  // Create separate handler for Aadhaar file changes
  const handleAadhaarFileChange = async (e, index) => {
    try {
      const file = e.currentTarget.files[0];
      setSelectedAadhaarImagesError(false);

      if (file && file.size > 10 * 1024 * 1024) {
        toast({
          title: "Please select a file less than 10 MB.",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const response = await axios.post(
        `${process.env.REACT_APP_BASE_URL}/api/coach/uploadImage`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      const url = response?.data?.url;
      if (url) {
        toast({
          title: "Image Uploaded Successfully",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }

      // Store just the URL string, not an object
      setSelectedAadhaarImages([...selectedAadhaarImages, url]);
      formik.setFieldValue(`aadhaarImage`, [
        ...selectedAadhaarImages,
        url,
      ]);
    } catch (error) {
      console.log(error);
      if (error?.response?.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  const formik = useFormik({
    initialValues: {
      kycDocuments: {
        documentName: coachData?.kycDocuments?.documentName,
        documentNumber: coachData?.kycDocuments?.documentNumber,
        documentImg: coachData?.kycDocuments?.documentImg,
      } || {
        documentName: "",
        documentNumber: "",
        documentImg: [],
      },
      aadhaarNumber: coachData?.aadhaarNumber || "",
      aadhaarImage: coachData?.aadhaarImage || [],
      bankDetails: coachData?.bankDetails || {
        accountNumber: "",
        accountHolderName: "",
        ifsc: "",
      },
      hasGst: coachData?.hasGst || false,
      gstNumber: coachData?.gstNumber || "",
      gstState: coachData?.gstState || "",
    },
    validationSchema: Yup.object().shape({
      kycDocuments: Yup.object().shape({
        documentNumber: Yup.string()
          .matches(panRegex, "Please enter a valid PAN number (e.g., **********)")
          .required("PAN Number is required"),
        documentImg: Yup.array().of(
          Yup.object().shape({
            url: Yup.string().url("Invalid URL"),
          })
        ),
      }),
      bankDetails: Yup.object().shape({
       accountNumber: Yup.string()
        .min(9, "Account number must be at least 9 digits")
        .max(18, "Account number cannot exceed 18 digits")
        .matches(/^\d+$/, "Account number must contain only digits")
        .required("Account number is required"),
      ifsc: Yup.string()
        .matches(
          /^[A-Z]{4}0[A-Z0-9]{6}$/,
          "IFSC code must be in format ABCD0123456"
        )
        .required("IFSC code is required"),
        accountHolderName: Yup.string()
          .required("Account Holder Name is required")
          .min(3, "Account holder name must be atleast 3 character")
          .max(
            100,
            "Account holder name must be less than or equal to 100 characters"
          ),
      }),
      hasGst: Yup.boolean(),
      // gstNumber: Yup.string().matches(
      //   gstRegex,
      //   "Please enter a valid GST number"
      // ),
      gstNumber: Yup.string()
      .matches(gstRegex, "Please enter a valid GST number")
      .when("hasGst", {
        is: true,
        then: (schema) => schema.required("GST Number is required"),
        otherwise: (schema) => schema.notRequired(),
      }),
    gstState: Yup.string()
      .when("hasGst", {
        is: true,
        then: (schema) => schema.required("GST State is required"),
        otherwise: (schema) => schema.notRequired(),
      })
      .max(100, "GST State must be less than or equal to 100 characters"),
      aadhaarNumber: Yup.string()
        .matches(/^\d{12}$/, "Aadhaar number must be exactly 12 digits")
        .required("Aadhaar number is required"),
      aadhaarImage: Yup.array()
        .min(2, "Please upload at least 2 Aadhaar images")
        .of(Yup.string().url("Invalid URL")),
    }),
    onSubmit: async (values) => {
      const filteredDocumentImg = values.kycDocuments.documentImg.filter(
        (doc) => doc.url !== ""
      );
      values.kycDocuments.documentImg = filteredDocumentImg;
      
      setIsLoadingSubmitBtn(true);
      
      if (selectedImages.length === 0) {
        setSelectedImagesError(true);
        setIsLoadingSubmitBtn(false);
        return;
      }
      
      if (selectedAadhaarImages.length < 2) {
        setSelectedAadhaarImagesError(true);
        setIsLoadingSubmitBtn(false);
        return;
      }
      
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: JSON.stringify(values),
      };

      axios
        .request(config)
        .then((response) => {
          setIsLoadingSubmitBtn(false);
          toast({
            title: "Coach KYC Details updated",
            status: "success",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log("Error in PATCH:", error);
          console.log("Error details:", {
            message: error.message,
            code: error.code,
            config: error.config,
            request: error.request
          });
          
          setIsLoadingSubmitBtn(false);

          // Check if it's a network error
          if (error.code === 'ERR_NETWORK' || error.message === 'Network Error') {
            toast({
              title: "Network Error",
              description: "Unable to connect to server. Please check if the server is running.",
              status: "error",
              duration: 6000,
              position: "top",
              isClosable: true,
            });
            return;
          }

          const status = error?.response?.status;

          if (status === 403) {
            toast({
              title: "You don't have access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong. Please try again later",
              description: error?.message || "Unknown error",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });
  useEffect(() => {
    setSelectedImages(coachData?.kycDocuments?.documentImg || []);
    setSelectedAadhaarImages(coachData?.aadhaarImage || []);
  }, [id]);

  return (
    <>
      <form>
        {/* Document Details */}
        <Card
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={0} flexBasis={"30%"}>
              Document Details
            </Heading>
            <FormControl
              mt={6}
              isInvalid={
                formik.touched.kycDocuments?.documentNumber &&
                formik.errors.kycDocuments?.documentNumber
              }
            >
              <FormLabel htmlFor="kycDocuments.documentNumber">
                PAN Card Number <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter PAN card number"
                name="kycDocuments.documentNumber"
                id="kycDocuments.documentNumber"
                autoComplete="none"
                {...formik.getFieldProps("kycDocuments.documentNumber")}
              />
              {formik.touched.kycDocuments?.documentNumber &&
                formik.errors.kycDocuments?.documentNumber && (
                  <FormErrorMessage>
                    {formik.errors.kycDocuments?.documentNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <Box mt={3}>
              <Input
                id={`kycDocuments.documentImg.${0}.url`}
                name={`kycDocuments.documentImg.${0}.url`}
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleFileChange(e, 0)}
              />
              {selectedImagesError && selectedImages.length === 0 && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  Please select atleast one document image
                </Text>
              )}
              {formik?.values?.kycDocuments?.documentImg?.length > 0 && (
                <Box display="flex" flexWrap="wrap" mt={3}>
                  {selectedImages.map(
                    (preview, index) =>
                      preview?.url && (
                        <Box key={index} m={1} textAlign={"center"}>
                          <a
                            href={`${preview?.url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Image
                              src={preview?.url}
                              alt={`Preview ${index}`}
                              height="11vh"
                              width="7vw"
                            />
                          </a>
                          {userData?.accessScopes?.coach?.includes(
                            "delete"
                          ) && (
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                deleteImageFiles(
                                  formik?.values?.kycDocuments?.documentImg[
                                    index
                                  ]?.url,
                                  index
                                );
                                formik.setFieldValue(
                                  `kycDocuments.documentImg[${index}].url`,
                                  ""
                                );
                                setSelectedImages(
                                  selectedImages.filter(
                                    (image) => image.url !== preview.url
                                  )
                                );
                              }}
                            >
                              Remove
                            </Button>
                          )}
                        </Box>
                      )
                  )}
                </Box>
              )}
            </Box>
            <FormControl
              mt={6}
              isInvalid={
                formik.touched.aadhaarNumber &&
                formik.errors.aadhaarNumber
              }
            >
              <FormLabel htmlFor="aadhaarNumber">
                Aadhar Number <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter Aadhar Card Number"
                name="aadhaarNumber"
                id="aadhaarNumber"
                autoComplete="none"
                {...formik.getFieldProps("aadhaarNumber")}
              />
              {formik.touched.aadhaarNumber &&
                formik.errors.aadhaarNumber && (
                  <FormErrorMessage>
                    {formik.errors.aadhaarNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <Box mt={3}>
              <Input
                id={`aadhaarImage.${0}.url`}
                name={`aadhaarImage.${0}.url`}
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleAadhaarFileChange(e, 0)}
              />
              {selectedAadhaarImagesError && selectedAadhaarImages.length < 2 && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  Please select at least 2 Aadhaar images
                </Text>
              )}
              {formik.errors.aadhaarImage && (
                <Text color={"red.500"} fontSize={"sm"} mt={1}>
                  {formik.errors.aadhaarImage}
                </Text>
              )}
              {selectedAadhaarImages.length > 0 && (
                <Box display="flex" flexWrap="wrap" mt={3}>
                  {selectedAadhaarImages.map(
                    (imageUrl, index) =>
                      imageUrl && (
                        <Box key={index} m={1} textAlign={"center"}>
                          <a
                            href={imageUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <Image
                              src={imageUrl}
                              alt={`Aadhaar Preview ${index}`}
                              height="11vh"
                              width="7vw"
                            />
                          </a>
                          {userData?.accessScopes?.coach?.includes("delete") && (
                            <Button
                              colorScheme="red"
                              size="sm"
                              mt={1}
                              onClick={() => {
                                const updatedImages = selectedAadhaarImages.filter(
                                  (url) => url !== imageUrl
                                );
                                setSelectedAadhaarImages(updatedImages);
                                formik.setFieldValue(`aadhaarImage`, updatedImages);
                              }}
                            >
                              Remove
                            </Button>
                          )}
                        </Box>
                      )
                  )}
                </Box>
              )}
            </Box>
          </CardBody>
        </Card>
        {/* Account Details */}
        <Card
          mt={4}
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              Account Details
            </Heading>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.accountHolderName &&
                formik.errors.bankDetails?.accountHolderName
              }
            >
              <FormLabel htmlFor="bankDetails.accountHolderName">
                Account Holder Name <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                placeholder="Enter account holder name"
                name="bankDetails.accountHolderName"
                id="bankDetails.accountHolderName"
                autoComplete="none"
                {...formik.getFieldProps("bankDetails.accountHolderName")}
              />
              {formik.touched.bankDetails?.accountHolderName &&
                formik.errors.bankDetails?.accountHolderName && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.accountHolderName}
                  </FormErrorMessage>
                )}
            </FormControl>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.accountNumber &&
                formik.errors.bankDetails?.accountNumber
              }
            >
              <FormLabel>
                Account No. <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                pattern="\d*"
                placeholder="Enter account number"
                name="bankDetails.accountNumber"
                id="bankDetails.accountNumber"
                {...formik.getFieldProps("bankDetails.accountNumber")}
                autoComplete="none"
              />
              {formik.touched.bankDetails?.accountNumber &&
                formik.errors.bankDetails?.accountNumber && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.accountNumber}
                  </FormErrorMessage>
                )}
            </FormControl>
            <FormControl
              mb={2}
              isInvalid={
                formik.touched.bankDetails?.ifsc &&
                formik.errors.bankDetails?.ifsc
              }
            >
              <FormLabel>
                IFSC Code <Text as="span" color="red.500">*</Text>
              </FormLabel>
              <Input
                type="text"
                name="bankDetails.ifsc"
                id="bankDetails.ifsc"
                autoComplete="none"
                placeholder="Enter IFSC code"
                {...formik.getFieldProps("bankDetails.ifsc")}
              />
              {formik.touched.bankDetails?.ifsc &&
                formik.errors.bankDetails?.ifsc && (
                  <FormErrorMessage>
                    {formik.errors.bankDetails?.ifsc}
                  </FormErrorMessage>
                )}
            </FormControl>
          </CardBody>
        </Card>

        {/*GST Details*/}

        <Card
          mt={4}
          pointerEvents={
            !userData?.accessScopes?.coach?.includes("write") ? "none" : "auto"
          }
        >
          <CardBody>
            <Heading as="h4" size="md" mb={4} flexBasis={"30%"}>
              GST Details
            </Heading>

            <FormControl
              isInvalid={formik.touched.hasGst && formik.errors.hasGst} // Validation handling
            >
              <FormLabel htmlFor="hasGst">Has GST?</FormLabel>
              <RadioGroup
                id="hasGst"
                name="hasGst"
                onChange={(value) =>
                  formik.setFieldValue("hasGst", value == "yes" ? true : false)
                }
                value={formik.values?.hasGst ? "yes" : "no"}
              >
                <HStack spacing="24px">
                  <Radio value="yes">Yes</Radio>
                  <Radio value="no">No</Radio>
                </HStack>
              </RadioGroup>
              <FormErrorMessage>{formik.errors.hasGst}</FormErrorMessage>
            </FormControl>

            {formik.values.hasGst && (
              <FormControl
                mb={2}
                isInvalid={formik.touched.gstNumber && formik.errors.gstNumber}
              >
                <FormLabel>GST No.</FormLabel>
                <Input
                  type="text"
                  pattern="\d*"
                  placeholder="Enter GST number"
                  name="gstNumber"
                  id="gstNumber"
                  {...formik.getFieldProps("gstNumber")}
                  autoComplete="none"
                />
                {formik.touched.gstNumber && formik.errors.gstNumber && (
                  <FormErrorMessage>{formik.errors.gstNumber}</FormErrorMessage>
                )}
              </FormControl>
            )}
            {formik.values.hasGst && (
              <FormControl
                flexBasis={"48%"}
                isInvalid={formik.touched.gstState && formik.errors.gstState}
              >
                <FormLabel htmlFor="gstState">GST State</FormLabel>

                {formik.values.hasGst && (
                  // Display the state name based on gstState (
                  // Render Select input if gstState is not available
                  <Select
                    placeholder="Select GST State"
                    name="gstState"
                    id="gstState"
                    autoComplete="address-level1"
                    {...formik.getFieldProps("gstState")}
                  >
                    <option value="">Select State</option>
                    {states.map((state) => (
                      <option key={state.isoCode} value={state.isoCode}>
                        {state.name}
                      </option>
                    ))}
                  </Select>
                )}

                <FormErrorMessage>{formik.errors.gstState}</FormErrorMessage>
              </FormControl>
            )}
          </CardBody>
        </Card>

        {userData?.accessScopes?.coach?.includes("write") && (
          <Flex justifyContent={"space-between"} alignItems={"center"} mt={4}>
            <Button
              colorScheme="red"
              flexBasis={"49%"}
              onClick={() => {
                formik.resetForm();
                toast({
                  title: "All Changes has been discarded",
                  status: "success",
                  duration: 4000,
                  position: "top",
                  isClosable: true,
                });
              }}
            >
              Discard
            </Button>
            <Button
              colorScheme="green"
              flexBasis={"49%"}
              type="submit"
              onClick={formik.handleSubmit}
              isLoading={isLoadingSubmitBtn}
            >
              Update
            </Button>
          </Flex>
        )}
      </form>
    </>
  );
};

export default KYCDetailsCoach;
