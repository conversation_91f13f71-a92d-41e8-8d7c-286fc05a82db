import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";

import {
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Flex,
  Box,
  Button,
  TableContainer,
  Table,
  Thead,
  Th,
  Tbody,
  Tr,
  Td,
  Text,
  Tooltip,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  FormControl,
  FormLabel,
  Textarea,
  Select,
  Input,
  Image,
  useToast,
  FormErrorMessage,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Spinner,
  useMediaQuery,
} from "@chakra-ui/react";
import { Link, useNavigate  } from "react-router-dom";
import axios from "axios";
import { MdDelete, MdDragHandle, MdEdit } from "react-icons/md";
import { useFormik } from "formik";
import * as Yup from "yup";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const Testimonials = () => {
  const navigate = useNavigate();
  const [deleteTestimonialId, setDeleteTestimonialId] = useState("");
  const [updateTestimonialId, setUpdateTestimonialId] = useState("");
  const [testimonialData, setTestimonialData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [selectedTestimonialData, setSelectedTestimonialData] = useState([]);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [previousTestimonialData, setPreviousTestimonialData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [renderMe, setRenderMe] = useState(0);
  const [isMobile] = useMediaQuery("(max-width: 768px)");

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const [isOpen2, setIsOpen2] = useState(false);
  const onClose2 = () => setIsOpen2(false);
  const onOpen2 = () => setIsOpen2(true);

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const getTestimonials = () => {
    const decodedToken = jwtDecode(token);
    const academyId = decodedToken.academyId._id;
    setTestimonialData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/${academyId}/testimonials`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setTestimonialData({
          result: response.data.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setTestimonialData({ result: [], isLoading: false, error: errorMessage });
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: errorMessage,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const renderMeComp = () => {
    setRenderMe((prev) => prev + 1);
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...testimonialData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setTestimonialData({ ...testimonialData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = testimonialData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/update/testimonial/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPreviousTestimonialData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        onClose1();
        setPosBtnLoading(false);
        setPreviousTestimonialData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getTestimonials();
    setDeleteTestimonialId("");
    setSelectedTestimonialData([]);
  }, [renderMe]);

  return (
    <Layout title="CMS | Testimonials">
      {/* Mobile Heading and Buttons in one row */}
      {isMobile ? (
        <Flex direction="column" alignItems="flex-start" gap={1} mb={3}>
          <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Testimonials</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && !adjustBtnEdit && (
            <Flex direction="row" gap={2} w="100%" justifyContent="flex-end">
              <Button
                variant={"outline"}
                colorScheme="telegram"
                size="xs"
                py={4}
                px={3}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  setAdjustBtnEdit(true);
                  setPreviousTestimonialData(testimonialData.result);
                }}
                mr={1}
                mb={0}
                isDisabled={adjustBtnEdit || testimonialData?.result?.length < 2}
              >
                Adjust Position
              </Button>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size="xs"
                py={4}
                px={3}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  onOpen1();
                  setSelectedTestimonialData([]);
                }}
                mr={0}
                mb={0}
                isDisabled={false}
              >
                Add Testimonials
              </Button>
            </Flex>
          )}
          {adjustBtnEdit && (
            <Flex direction="row" gap={2} w="100%" justifyContent="flex-end">
              <Button
                variant={"outline"}
                colorScheme="red"
                size="xs"
                py={4}
                px={3}
                w="35%"
                fontSize="xs"
                onClick={() => {
                  setTestimonialData({
                    result: previousTestimonialData,
                    isLoading: false,
                    error: false,
                  });
                  setAdjustBtnEdit(false);
                }}
                mr={1}
                mb={0}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size="xs"
                py={4}
                px={3}
                w="35%"
                fontSize="xs"
                isLoading={posBtnLoading}
                onClick={updateBlockPosition}
                mr={0}
                mb={0}
              >
                Save Changes
              </Button>
            </Flex>
          )}
        </Flex>
      ) : (
        <Flex justifyContent={{ base: "flex-start", md: "space-between" }} alignItems={{ base: "flex-start", md: "center" }} direction={{ base: "column", md: "row" }} mb={{ base: 2, md: 4 }}>
          <Flex alignItems="center" gap={0}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Testimonials</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          <Box mt={{ base: 2, md: 0 }}>
            {!adjustBtnEdit ? (
              userData?.accessScopes?.cms?.includes("write") && (
                <Flex direction="row" gap={2}>
                  <Button
                    variant={"outline"}
                    colorScheme="telegram"
                    size={{ base: "xs", md: "sm" }}
                    py={{ base: 2, md: 5 }}
                    px={{ base: 2, md: 4 }}
                    onClick={() => {
                      setAdjustBtnEdit(true);
                      setPreviousTestimonialData(testimonialData.result);
                    }}
                    flexShrink={0}
                    isDisabled={testimonialData?.result?.length < 2}
                  >
                    Adjust Position
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="teal"
                    size={{ base: "xs", md: "sm" }}
                    py={{ base: 2, md: 5 }}
                    px={{ base: 2, md: 4 }}
                    onClick={() => {
                      onOpen1();
                      setSelectedTestimonialData([]);
                    }}
                    isDisabled={false}
                    flexShrink={0}
                  >
                    Add Testimonials
                  </Button>
                </Flex>
              )
            ) : (
              <Flex wrap="nowrap" gap={2}>
                <Button
                  variant={"outline"}
                  colorScheme="red"
                  size={{ base: "xs", md: "sm" }}
                  py={{ base: 2, md: 5 }}
                  px={{ base: 2, md: 4 }}
                  onClick={() => {
                    setTestimonialData({
                      result: previousTestimonialData,
                      isLoading: false,
                      error: false,
                    });
                    setAdjustBtnEdit(false);
                  }}
                  flexShrink={0}
                >
                  Discard
                </Button>
                <Button
                  variant={"outline"}
                  colorScheme="green"
                  size={{ base: "xs", md: "sm" }}
                  py={{ base: 2, md: 5 }}
                  px={{ base: 2, md: 4 }}
                  isLoading={posBtnLoading}
                  onClick={updateBlockPosition}
                  flexShrink={0}
                >
                  Save Changes
                </Button>
              </Flex>
            )}
          </Box>
        </Flex>
      )}
      {/* Mobile Card View */}
      {isMobile ? (
        !testimonialData?.isLoading && testimonialData?.error ? (
          <Flex
            justifyContent={{ base: "flex-start", md: "center" }}
            alignItems={{ base: "flex-start", md: "center" }}
            w={"full"}
            my={{ base: 4, md: 10 }}
            px={{ base: 2, md: 0 }}
          >
            <Text color={"red.500"} fontSize={{ base: "sm", md: "md" }}>
              {testimonialData?.error}
            </Text>
          </Flex>
        ) : testimonialData?.isLoading ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={6}>
            <Spinner size="md" />
          </Flex>
        ) : (
          <Box mt={2}>
            {testimonialData.result.map((testimonial, index) => (
              <Box
                key={testimonial._id || index}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
                p={4}
                mb={3}
                bg="white"
              >
                {/* Name */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Name:</Text>
                  <Text fontSize="sm">{testimonial?.name || "n/a"}</Text>
                </Flex>
                {/* Description */}
                <Flex justify="space-between" align="center">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Description:</Text>
                  <Text fontSize="sm">{testimonial?.description?.length > 40 ? testimonial.description.substring(0, 40) + '...' : testimonial?.description || "n/a"}</Text>
                </Flex>
                <Flex justifyContent="flex-end" gap={3} mt={2}>
                  {userData?.accessScopes?.cms?.includes("write") && (
                    <Tooltip label="Edit Testimonials">
                      <Text
                        as="span"
                        cursor={"pointer"}
                        fontSize="20px"
                        onClick={() => {
                          onOpen3();
                          setUpdateTestimonialId(testimonial._id);
                          setSelectedTestimonialData({
                            name: testimonial?.name || "",
                            description: testimonial?.description || "",
                          });
                        }}
                      >
                        <MdEdit />
                      </Text>
                    </Tooltip>
                  )}
                  {userData?.accessScopes?.cms?.includes("delete") && (
                    <Tooltip label="Delete Testimonials">
                      <Text
                        as="span"
                        cursor={"pointer"}
                        fontSize="20px"
                        onClick={() => {
                          setDeleteTestimonialId(testimonial._id);
                          onOpen2();
                        }}
                      >
                        <MdDelete />
                      </Text>
                    </Tooltip>
                  )}
                  {adjustBtnEdit && (
                    <Text as="span" ml={2}>
                      <MdDragHandle style={{ cursor: "grab" }} fontSize="24px" />
                    </Text>
                  )}
                </Flex>
              </Box>
            ))}
          </Box>
        )
      ) : (
        !testimonialData?.isLoading && testimonialData?.error ? (
          <Flex
            justifyContent={{ base: "flex-start", md: "center" }}
            alignItems={{ base: "flex-start", md: "center" }}
            w={"full"}
            my={{ base: 4, md: 10 }}
            px={{ base: 2, md: 0 }}
          >
            <Text color={"red.500"} fontSize={{ base: "sm", md: "md" }}>
              {testimonialData?.error}
            </Text>
          </Flex>
        ) : (
          <TableContainer
            mt={{ base: 2, md: 6 }}
            height={{ base: "auto", md: `${window.innerHeight - 222}px` }}
            maxHeight={{ base: "60vh", md: `${window.innerHeight - 222}px` }}
            overflowY={"auto"}
            px={{ base: 1, md: 0 }}
          >
            <Table variant="simple" size={{ base: "sm", md: "md" }}>
              <Thead
                bgColor={"#c1eaee"}
                position={"sticky"}
                top={"0px"}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Position</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Name</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Description</Th>
                  <Th textAlign={{ base: "left", md: "center" }} fontSize={{ base: "xs", md: "xs" }}>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {testimonialData?.isLoading && !testimonialData?.error ? (
                  <Tr>
                    <Td> </Td>
                    <Td></Td>
                    <Td
                      display={{ base: "block", md: "flex" }}
                      justifyContent={{ base: "flex-start", md: "center" }}
                      alignItems={{ base: "flex-start", md: "center" }}
                    >
                      <Spinner size={{ base: "xs", md: "md" }} />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : testimonialData?.result?.length === 0 ? (
                  <Tr>
                    <Td colSpan={4} textAlign="center" py={10}>
                      <Text color="gray.500" fontSize="md" fontWeight="semibold">No data to show</Text>
                    </Td>
                  </Tr>
                ) : (
                  testimonialData.result.map((testimonial, index) => {
                    return (
                      <>
                        {adjustBtnEdit ? (
                          <Tr
                            key={index}
                            draggable
                            onDragStart={() => handleDragStart(index)}
                            onDragOver={() => handleDragOver(index)}
                            onDragEnd={handleDragEnd}
                          >
                            <Td fontSize={{ base: "xs", md: "14px" }}>{index + 1}.</Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              fontSize={{ base: "xs", md: "14px" }}
                            >
                              {testimonial?.name}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {testimonial?.description?.substring(0, 20) + "..."}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {!adjustBtnEdit ? (
                                <Flex
                                  justifyContent={{ base: "flex-start", md: "space-evenly" }}
                                  alignItems={{ base: "flex-start", md: "center" }}
                                  direction={{ base: "column", md: "row" }}
                                  gap={{ base: 2, md: 0 }}
                                >
                                  {userData?.accessScopes?.cms?.includes(
                                    "write"
                                  ) && (
                                    <Tooltip label="Edit Testimonials">
                                      <Text
                                        as="span"
                                        cursor={"pointer"}
                                        fontSize={{ base: "md", md: "20px" }}
                                        onClick={() => {
                                          onOpen3();
                                          setUpdateTestimonialId(testimonial._id);
                                          setSelectedTestimonialData({
                                            name: testimonial?.name || "",
                                            description:
                                              testimonial?.description || "",
                                          });
                                        }}
                                      >
                                        <MdEdit />
                                      </Text>
                                    </Tooltip>
                                  )}
                                  {userData?.accessScopes?.cms?.includes(
                                    "delete"
                                  ) && (
                                    <Tooltip label="Delete Testimonials">
                                      <Text
                                        as="span"
                                        cursor={"pointer"}
                                        fontSize={{ base: "md", md: "20px" }}
                                        onClick={() => {
                                          setDeleteTestimonialId(testimonial._id);
                                          onOpen2();
                                        }}
                                      >
                                        <MdDelete />
                                      </Text>
                                    </Tooltip>
                                  )}
                                </Flex>
                              ) : (
                                <Flex
                                  justifyContent={{ base: "flex-start", md: "center" }}
                                  alignItems={{ base: "flex-start", md: "center" }}
                                >
                                  <Text as={"span"} ml={{ base: 1, md: 3 }}>
                                    <MdDragHandle
                                      style={{ cursor: "grab" }}
                                      fontSize={{ base: "lg", md: "24px" }}
                                    />
                                  </Text>
                                </Flex>
                              )}
                            </Td>
                          </Tr>
                        ) : (
                          <Tr key={index}>
                            <Td fontSize={{ base: "xs", md: "14px" }}>{index + 1}.</Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              fontSize={{ base: "xs", md: "14px" }}
                            >
                              {testimonial?.name}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {testimonial?.description?.substring(0, 20) + "..."}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {!adjustBtnEdit ? (
                                <Flex
                                  justifyContent={{ base: "flex-start", md: "space-evenly" }}
                                  alignItems={{ base: "flex-start", md: "center" }}
                                  direction={{ base: "column", md: "row" }}
                                  gap={{ base: 2, md: 0 }}
                                >
                                  {userData?.accessScopes?.cms?.includes(
                                    "write"
                                  ) && (
                                    <Tooltip label="Edit Testimonials">
                                      <Text
                                        as="span"
                                        cursor={"pointer"}
                                        fontSize={{ base: "md", md: "20px" }}
                                        onClick={() => {
                                          onOpen3();
                                          setUpdateTestimonialId(testimonial._id);
                                          setSelectedTestimonialData({
                                            name: testimonial?.name || "",
                                            description:
                                              testimonial?.description || "",
                                          });
                                        }}
                                      >
                                        <MdEdit />
                                      </Text>
                                    </Tooltip>
                                  )}
                                  {userData?.accessScopes?.cms?.includes(
                                    "delete"
                                  ) && (
                                    <Tooltip label="Delete Testimonials">
                                      <Text
                                        as="span"
                                        cursor={"pointer"}
                                        fontSize={{ base: "md", md: "20px" }}
                                        onClick={() => {
                                          setDeleteTestimonialId(testimonial._id);
                                          onOpen2();
                                        }}
                                      >
                                        <MdDelete />
                                      </Text>
                                    </Tooltip>
                                  )}
                                </Flex>
                              ) : (
                                <Flex
                                  justifyContent={{ base: "flex-start", md: "center" }}
                                  alignItems={{ base: "flex-start", md: "center" }}
                                >
                                  <Text as={"span"} ml={{ base: 1, md: 3 }}>
                                    <MdDragHandle
                                      style={{ cursor: "grab" }}
                                      fontSize={{ base: "lg", md: "24px" }}
                                    />
                                  </Text>
                                </Flex>
                              )}
                            </Td>
                          </Tr>
                        )}
                      </>
                    );
                  })
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )
      )}
      {/* Testimonials add Data Component */}
      <TestimonialsData
        renderMeComp={renderMeComp}
        onOpen1={onOpen1}
        onOpen2={onOpen2}
        isOpen1={isOpen1}
        isOpen2={isOpen2}
        onClose1={onClose1}
        onClose2={onClose2}
        deleteTestimonialId={deleteTestimonialId}
        selectedTestimonialData={selectedTestimonialData}
      />
      {/* Testimonials update Data Component */}
      <TestimonialsDataUpdate
        renderMeComp={renderMeComp}
        onOpen3={onOpen3}
        isOpen3={isOpen3}
        onClose3={onClose3}
        updateTestimonialId={updateTestimonialId}
        selectedTestimonialData={selectedTestimonialData}
      />
    </Layout>
  );
};

export default Testimonials;

const TestimonialsData = ({
  selectedTestimonialData,
  renderMeComp,
  onClose1,
  onClose2,
  deleteTestimonialId,
  isOpen1,
  isOpen2,
}) => {
  const [deleteBtnLoading, setDeleteBtnLoading] = useState(false);
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const validationSchema = Yup.object().shape({
    description: Yup.string()
      .required("Description is required")
      .min(10, "Description must be at least 10 characters"),
    name: Yup.string()
      .required("Full name is required")
      .min(3, "Full name must be at least 3 characters"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values, { resetForm }) => {
      setIsBtnLoading(true);
      let data = JSON.stringify(values);
      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/create/testimonial`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          onClose1();
          setIsBtnLoading(false);
          renderMeComp();
          resetForm();
          toast({
            title: "Testimonial added",
            status: "success",
            duration: 3500,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          setIsBtnLoading(false);
          if (error.response?.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  const deleteTestimonial = () => {
    setDeleteBtnLoading(true);
    let config = {
      method: "delete",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/delete/testimonial/${deleteTestimonialId}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        setDeleteBtnLoading(false);
        renderMeComp();
        onClose2();
        toast({
          title: "Testimonial deleted",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setDeleteBtnLoading(false);
        onClose2();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    // Update formik initial values whenever selectedTestimonialData changes
    formik.setValues({
      name: selectedTestimonialData?.name || "",
      description: selectedTestimonialData?.description || "",
    });
  }, [selectedTestimonialData]);

  return (
    <>
      {/* Add Testimonial Modal - Responsive */}
      <form onSubmit={formik.handleSubmit}>
        <Modal isOpen={isOpen1} onClose={onClose1} size={{ base: "sm", md: "xl" }} isCentered>
          <ModalOverlay />
          <ModalContent
            mx={{ base: 4, md: 0 }}
            my={{ base: 8, md: 0 }}
            borderRadius={{ base: "lg", md: "lg" }}
            w={{ base: "100%", md: "auto" }}
            maxW={{ base: "95vw", md: "600px" }}
          >
            <ModalHeader fontSize={{ base: "lg", md: "2xl" }}>Add Testimonial</ModalHeader>
            <ModalCloseButton />
            <ModalBody px={{ base: 5, md: 10 }} py={{ base: 5, md: 8 }}>
              <FormControl
                isInvalid={formik.errors.name && formik.touched.name}
                mb={4}
              >
                <FormLabel fontSize={{ base: "sm", md: "md" }}>Name</FormLabel>
                <Input
                  type="text"
                  placeholder="Enter testimonial full name"
                  id="name"
                  name="name"
                  onChange={formik.handleChange}
                  value={formik.values.name}
                  fontSize={{ base: "sm", md: "md" }}
                  w="100%"
                />
                {formik.errors.name && formik.touched.name && (
                  <FormErrorMessage fontSize={{ base: "xs", md: "sm" }}>{formik.errors.name}</FormErrorMessage>
                )}
              </FormControl>
              <FormControl
                my={3}
                isInvalid={formik.errors.description && formik.touched.description}
                mb={2}
              >
                <FormLabel fontSize={{ base: "sm", md: "md" }}>Description</FormLabel>
                <Textarea
                  placeholder="Enter testimonial description"
                  id="description"
                  name="description"
                  onChange={formik.handleChange}
                  value={formik.values.description}
                  fontSize={{ base: "sm", md: "md" }}
                  w="100%"
                  minH={{ base: "80px", md: "120px" }}
                />
                {formik.errors.description && formik.touched.description && (
                  <FormErrorMessage fontSize={{ base: "xs", md: "sm" }}>
                    {formik.errors.description}
                  </FormErrorMessage>
                )}
              </FormControl>
            </ModalBody>
            <ModalFooter flexDirection={{ base: "column", md: "row" }} gap={2}>
              <Button
                colorScheme="red"
                mr={{ base: 0, md: 3 }}
                mb={{ base: 2, md: 0 }}
                w={{ base: "100%", md: "auto" }}
                onClick={() => {
                  onClose1();
                  formik.resetForm();
                }}
              >
                Discard
              </Button>
              <Button
                colorScheme="green"
                size="sm"
                type="submit"
                onClick={formik.handleSubmit}
                isLoading={isBtnLoading}
                w={{ base: "100%", md: "auto" }}
              >
                Save Changes
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </form>

      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose2}
        isOpen={isOpen2}
        isCentered
      >
        <AlertDialogOverlay />
        <AlertDialogContent maxW={{ base: 'sm', md: '500px' }} w={{ base: '100%', md: 'auto' }}>
          <AlertDialogHeader>Delete Testimonial</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              onClick={() => {
                onClose2();
              }}
            >
              No
            </Button>
            <Button
              colorScheme="telegram"
              ml={3}
              isLoading={deleteBtnLoading}
              onClick={deleteTestimonial}
            >
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

const TestimonialsDataUpdate = ({
  selectedTestimonialData,
  renderMeComp,
  onClose3,
  isOpen3,
  updateTestimonialId,
}) => {
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const validationSchema = Yup.object().shape({
    description: Yup.string()
      .required("Description is required")
      .min(10, "Description must be at least 10 characters"),
    name: Yup.string()
      .required("Full name is required")
      .min(3, "Full name must be at least 3 characters"),
  });

  const formik = useFormik({
    initialValues: {
      name: "",
      description: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values, { resetForm }) => {
      setIsBtnLoading(true);
      let data = JSON.stringify(values);
      let config = {
        method: "patch",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/update/testimonial/${updateTestimonialId}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          onClose3();
          setIsBtnLoading(false);
          renderMeComp();
          resetForm();
          toast({
            title: "Testimonial updated",
            status: "success",
            duration: 3500,
            position: "top",
            isClosable: true,
          });
        })
        .catch((error) => {
          console.log(error);
          onClose3();
          setIsBtnLoading(false);
          resetForm();
          if (error.response.status === 403) {
            toast({
              title: "You don't have an access to perform this action",
              status: "warning",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          } else {
            toast({
              title: "Something went wrong please try again later",
              status: "error",
              duration: 4000,
              position: "top",
              isClosable: true,
            });
          }
        });
    },
  });

  useEffect(() => {
    // Update formik initial values whenever selectedTestimonialData changes
    formik.setValues({
      name: selectedTestimonialData?.name || "",
      description: selectedTestimonialData?.description || "",
    });
  }, [selectedTestimonialData]);

  return (
    <>
      {/* Add Testimonial Modal */}
      <Modal isOpen={isOpen3} onClose={onClose3} size={{ base: "sm", md: "xl" }} isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{"Update Testimonial"}</ModalHeader>
          <ModalCloseButton />
          <form onSubmit={formik.handleSubmit} style={{ width: "100%" }}>
            <ModalBody>
              <FormControl
                isInvalid={formik.errors.name && formik.touched.name}
              >
                <FormLabel>Name</FormLabel>
                <Input
                  type="text"
                  placeholder="Enter testimonial full name"
                  id="name"
                  name="name"
                  onChange={formik.handleChange}
                  value={formik.values.name}
                />
                {formik.errors.name && formik.touched.name && (
                  <FormErrorMessage>{formik.errors.name}</FormErrorMessage>
                )}
              </FormControl>
              <FormControl
                my={3}
                isInvalid={
                  formik.errors.description && formik.touched.description
                }
              >
                <FormLabel>Description</FormLabel>
                <Textarea
                  placeholder="Enter testimonial description"
                  id="description"
                  name="description"
                  onChange={formik.handleChange}
                  value={formik.values.description}
                />
                {formik.errors.description && formik.touched.description && (
                  <FormErrorMessage>
                    {formik.errors.description}
                  </FormErrorMessage>
                )}
              </FormControl>
            </ModalBody>
            <ModalFooter>
              <Button
                colorScheme="red"
                mr={3}
                onClick={() => {
                  onClose3();
                }}
              >
                Discard
              </Button>
              <Button
                colorScheme="green"
                size="sm"
                type="submit"
                isLoading={isBtnLoading}
                onClick={formik.handleSubmit}
              >
                Save Changes
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </>
  );
};
