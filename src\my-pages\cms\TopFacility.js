import React, { useEffect, useState } from "react";
import { useMediaQuery } from "@chakra-ui/react";
import Layout from "../../layout/default";
import { MdDelete, MdDragHandle, MdPowerSettingsNew } from "react-icons/md";
import {
  Box,
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Button,
  Text,
  useDisclosure,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  useToast,
  Spinner,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  Flex,
  Badge,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";

import { useSelector } from "react-redux";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const TopFacility = () => {
  const navigate = useNavigate();
  const [facilityData, setFacilityData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });

  const [isUpdated, setIsUpdated] = useState(false);
  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const [saveChangesBtnLoading, setSaveChangesBtnLoading] = useState(false);
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevFacilityData, setPrevFacilityData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);
  const [isDeleted, setIsDeleted] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];
  const userData = useSelector((state) => state.user);

  const [statusModal, setStatusModal] = useState({ isOpen: false, facility: null });
  const [isMobile] = useMediaQuery("(max-width: 768px)");

  const getFacilityData = () => {
    setFacilityData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/facilities`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setFacilityData({
          result: response.data.data.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setFacilityData({ result: [], isLoading: false, error: errorMessage });
        toast({
          title: errorMessage,
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      });
  };

  // Removed add facility API and its button. updateAndSave now only handles position update if needed.
  const updateAndSave = () => {
    onClose1();
    setSaveChangesBtnLoading(true);
    updateBlockPosition();
    setSaveChangesBtnLoading(false);
    setIsUpdated(false);
    onClose();
    setIsDeleted(false);
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...facilityData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setFacilityData({ ...facilityData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  // Toggle facility status with confirmation modal
  const handleStatusIconClick = (facility) => {
    setStatusModal({ isOpen: true, facility });
  };

  const handleConfirmStatusChange = async () => {
    if (!statusModal.facility) return;
    try {
      await axios.patch(
        `${process.env.REACT_APP_BASE_URL}/api/academy-cms/facilities/${statusModal.facility.facilityId}/toggle-status`,
        {},
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      toast({
        title: "Facility status updated",
        status: "success",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
      getFacilityData();
    } catch (error) {
      toast({
        title: "Failed to update status",
        status: "error",
        duration: 3000,
        position: "top",
        isClosable: true,
      });
    }
    setStatusModal({ isOpen: false, facility: null });
  };

  const handleCancelStatusChange = () => {
    setStatusModal({ isOpen: false, facility: null });
  };

  // Update facility positions
  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = facilityData.result.map((x, idx) => {
      return { id: x.facilityId, newPosition: idx + 1 };
    });
    let data = JSON.stringify({ updates: value });
    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/facilities/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };
    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevFacilityData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Facility position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
        getFacilityData();
      })
      .catch((error) => {
        setPosBtnLoading(false);
        setPrevFacilityData([]);
        setAdjustBtnEdit(false);
        toast({
          title: "Failed to update position",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      });
  };

  useEffect(() => {
    getFacilityData();
  }, []);

  return (
    <Layout title="CMS | Facilities">
      {/* Responsive Back Button, Breadcrumb, and Action Buttons */}
      {isMobile ? (
        <Flex direction="column" alignItems="flex-start" gap={1} mb={3}>
          <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Facilities</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && (
            !adjustBtnEdit ? (
              <Flex direction="row" gap={2} w="100%">
                <Button
                  variant={"outline"}
                  colorScheme="telegram"
                  size="xs"
                  py={3}
                  px={3}
                  w="auto"
                  fontSize="xs"
                  onClick={() => {
                    setAdjustBtnEdit(true);
                    setPrevFacilityData(facilityData.result);
                  }}
                  mr={1}
                  mb={0}
                  isDisabled={adjustBtnEdit || facilityData?.result?.length < 2}
                >
                  Adjust Position
                </Button>
              </Flex>
            ) : (
              <Flex direction="row" gap={2} w="100%">
                <Button
                  variant={"outline"}
                  colorScheme="red"
                  size="xs"
                  py={3}
                  px={3}
                  w="auto"
                  fontSize="xs"
                  onClick={() => {
                    setFacilityData({
                      result: prevFacilityData,
                      isLoading: false,
                      error: false,
                    });
                    setAdjustBtnEdit(false);
                  }}
                  mr={1}
                  mb={0}
                >
                  Discard
                </Button>
                <Button
                  variant={"outline"}
                  colorScheme="green"
                  size="xs"
                  py={3}
                  px={3}
                  w="auto"
                  fontSize="xs"
                  isLoading={posBtnLoading}
                  onClick={updateBlockPosition}
                  mr={0}
                  mb={0}
                >
                  Save Changes
                </Button>
              </Flex>
            )
          )}
        </Flex>
      ) : (
        <Flex justifyContent={{ base: "flex-start", md: "space-between" }} alignItems={{ base: "flex-start", md: "center" }} direction={{ base: "column", md: "row" }} wrap="wrap" px={{ base: 0, md: 0 }}>
          <Flex alignItems="center" gap={0}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Facilities</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          <Box mt={{ base: 2, md: 0 }}>
            {userData?.accessScopes?.cms?.includes("write") && (
              !adjustBtnEdit ? (
                <Button
                  variant={"outline"}
                  colorScheme="telegram"
                  size={{ base: "xs", md: "sm" }}
                  py={{ base: 2, md: 5 }}
                  px={{ base: 2, md: 4 }}
                  onClick={() => {
                    setAdjustBtnEdit(true);
                    setPrevFacilityData(facilityData.result);
                  }}
                  flexShrink={0}
                  isDisabled={facilityData?.result?.length < 2}
                >
                  Adjust Position
                </Button>
              ) : (
                <Flex wrap="nowrap" gap={{ base: 2, md: 3 }}>
                  <Button
                    variant={"outline"}
                    colorScheme="red"
                    size={{ base: "xs", md: "sm" }}
                    py={{ base: 2, md: 5 }}
                    px={{ base: 2, md: 4 }}
                    onClick={() => {
                      setFacilityData({
                        result: prevFacilityData,
                        isLoading: false,
                        error: false,
                      });
                      setAdjustBtnEdit(false);
                    }}
                    flexShrink={0}
                  >
                    Discard
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="green"
                    size={{ base: "xs", md: "sm" }}
                    py={{ base: 2, md: 5 }}
                    px={{ base: 2, md: 4 }}
                    isLoading={posBtnLoading}
                    onClick={updateBlockPosition}
                    flexShrink={0}
                  >
                    Save Changes
                  </Button>
                </Flex>
              )
            )}
          </Box>
        </Flex>
      )}
      {/* Facility List: Card view on mobile, table on desktop */}
      {isMobile ? (
        !facilityData?.isLoading && facilityData?.error ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={10}>
            <Text color="red.500">{facilityData?.error}</Text>
          </Flex>
        ) : facilityData?.isLoading ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={6}>
            <Spinner size="md" />
          </Flex>
        ) : facilityData?.result?.length === 0 ? (
          <Flex justifyContent="center" alignItems="center" minH="120px">
            <Text color="gray.400">No facilities to show</Text>
          </Flex>
        ) : (
          <Box mt={2}>
            {facilityData?.result?.map((facility, inx) => {
              const details = facility?.academy?.facilityDetails || {};
              return (
                <Box
                  key={facility._id}
                  borderWidth="1px"
                  borderRadius="lg"
                  boxShadow="sm"
                  p={4}
                  mb={3}
                  bg="white"
                >
                  {/* Name */}
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontWeight="semibold" fontSize="sm" mb={0}>Name:</Text>
                    <Text fontSize="sm">{details.name || "n/a"}</Text>
                  </Flex>
                  {/* Pin Code */}
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontWeight="semibold" fontSize="sm" mb={0}>Pin Code:</Text>
                    <Text fontSize="sm">{details.pinCode || "n/a"}</Text>
                  </Flex>
                  {/* State */}
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontWeight="semibold" fontSize="sm" mb={0}>State:</Text>
                    <Text fontSize="sm">{details.state || "n/a"}</Text>
                  </Flex>
                  {/* City */}
                  <Flex justify="space-between" align="center" mb="10px">
                    <Text fontWeight="semibold" fontSize="sm" mb={0}>City:</Text>
                    <Text fontSize="sm">{details.city || "n/a"}</Text>
                  </Flex>
                  {/* Status */}
                  <Flex justify="space-between" align="center">
                    <Text fontWeight="semibold" fontSize="sm" mb={0}>Status:</Text>
                    <Badge
                      fontSize="0.8em"
                      px={2}
                      borderRadius="md"
                      bg={facility.isActive ? "#38A169" : "#E53E3E"}
                      color="white"
                    >
                      {facility.isActive ? "ACTIVE" : "INACTIVE"}
                    </Badge>
                  </Flex>
                  <Flex justifyContent="flex-end" gap={3} mt={2}>
                    {!adjustBtnEdit && (
                      <Text as="span" ml={1} cursor="pointer" onClick={() => handleStatusIconClick(facility)}>
                        <MdPowerSettingsNew
                          fontSize="20px"
                          color={facility.isActive ? "#38A169" : "#E53E3E"}
                          title={facility.isActive ? "Deactivate" : "Activate"}
                        />
                      </Text>
                    )}
                    {adjustBtnEdit && (
                      <Text as="span" ml={2}>
                        <MdDragHandle style={{ cursor: "grab" }} fontSize="24px" />
                      </Text>
                    )}
                  </Flex>
                </Box>
              );
            })}
          </Box>
        )
      ) : (
        !facilityData?.isLoading && facilityData?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              {facilityData?.error}
            </Text>
          </Flex>
        ) : (
          <TableContainer
            mt={{ base: 2, md: 6 }}
            height={{ base: "auto", md: `${window.innerHeight - 200}px` }}
            overflowY={{ base: "visible", md: "scroll" }}
            px={{ base: 1, md: 0 }}
          >
            <Table variant="simple" size={{ base: "sm", md: "md" }}>
              <Thead
                bgColor={"#c1eaee"}
                position={{ base: "static", md: "sticky" }}
                top={{ base: "auto", md: "0px" }}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Position</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Name</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Pin Code</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>State</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>City</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Status</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }} textAlign={"center"}>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {facilityData?.isLoading && !facilityData?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td> </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={{ base: "block", md: "flex" }}
                      justifyContent={{ base: "center", md: "center" }}
                      alignItems={{ base: "center", md: "center" }}
                    >
                      <Spinner />
                    </Td>
                  </Tr>
                ) : facilityData?.result?.length === 0 ? (
                  <Tr>
                    <Td colSpan={7} textAlign="center" py={8}>
                      <Text color="gray.400">No facilities to show</Text>
                    </Td>
                  </Tr>
                ) : (
                  facilityData?.result?.map((facility, inx) => {
                    const details = facility?.academy?.facilityDetails || {};
                    return (
                      <Tr
                        key={facility._id}
                        draggable={adjustBtnEdit}
                        onDragStart={
                          adjustBtnEdit ? () => handleDragStart(inx) : undefined
                        }
                        onDragOver={
                          adjustBtnEdit ? () => handleDragOver(inx) : undefined
                        }
                        onDragEnd={adjustBtnEdit ? handleDragEnd : undefined}
                      >
                        <Td fontSize={{ base: "xs", md: "14px" }}>{inx + 1 + "."}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{details.name || "n/a"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{details.pinCode || "n/a"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{details.state || "n/a"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>{details.city || "n/a"}</Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>
                          <Badge colorScheme={facility.isActive ? "green" : "red"}>
                            {facility.isActive ? "ACTIVE" : "INACTIVE"}
                          </Badge>
                        </Td>
                        <Td fontSize={{ base: "xs", md: "14px" }}>
                          <Flex justifyContent={{ base: "flex-start", md: "center" }} alignItems={{ base: "flex-start", md: "center" }}>
                            {/* Status toggle icon only when not adjusting */}
                            {!adjustBtnEdit && (
                              <Text as={"span"} ml={1} cursor="pointer" onClick={() => handleStatusIconClick(facility)}>
                                <MdPowerSettingsNew
                                  fontSize={{ base: "18px", md: "22px" }}
                                  color={facility.isActive ? "#38A169" : "#E53E3E"}
                                  title={facility.isActive ? "Deactivate" : "Activate"}
                                />
                              </Text>
                            )}
                            {/* Drag handle only in adjust mode */}
                            {adjustBtnEdit && (
                              <Text as={"span"} ml={3}>
                                <MdDragHandle
                                  style={{
                                    cursor: "grab",
                                  }}
                                  fontSize={{ base: "18px", md: "24px" }}
                                />
                              </Text>
                            )}
                          </Flex>
                        </Td>
                      </Tr>
                    );
                  })
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )
      )}
    
{/* Save Changes Alert */}
<AlertDialog
  motionPreset="slideInBottom"
  onClose={onClose1}
  isOpen={isOpen1}
  isCentered
>
  <AlertDialogOverlay />
  <AlertDialogContent
    w={{ base: "90%", md: "400px" }}
    maxW="90%"
    borderRadius="lg"
    p={{ base: 4, md: 6 }}
  >
    <AlertDialogHeader fontSize={{ base: "lg", md: "xl" }}>
      Save Changes
    </AlertDialogHeader>
    <AlertDialogCloseButton />
    <AlertDialogBody fontSize={{ base: "sm", md: "md" }}>
      Are you sure you want to make these changes.
    </AlertDialogBody>
    <AlertDialogFooter>
      <Button
        size={{ base: "sm", md: "md" }}
        onClick={onClose1}
      >
        No
      </Button>
      <Button
        colorScheme="telegram"
        size={{ base: "sm", md: "md" }}
        ml={3}
        onClick={() => updateAndSave()}
      >
        Yes
      </Button>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>

{/* Status Change Alert */}
<AlertDialog
  motionPreset="slideInBottom"
  onClose={handleCancelStatusChange}
  isOpen={statusModal.isOpen}
  isCentered
>
  <AlertDialogOverlay />
  <AlertDialogContent
    w={{ base: "90%", md: "400px" }}
    maxW="90%"
    borderRadius="lg"
    p={{ base: 4, md: 6 }}
  >
    <AlertDialogHeader fontSize={{ base: "lg", md: "xl" }}>
      Update Facility Status
    </AlertDialogHeader>
    <AlertDialogCloseButton />
    <AlertDialogBody fontSize={{ base: "sm", md: "md" }}>
      {statusModal.facility?.isActive
        ? "Are you sure you want to deactivate this facility?"
        : "Are you sure you want to activate this facility?"}
    </AlertDialogBody>
    <AlertDialogFooter>
      <Button
        size={{ base: "sm", md: "md" }}
        onClick={handleCancelStatusChange}
      >
        No
      </Button>
      <Button
        colorScheme={statusModal.facility?.isActive ? "red" : "green"}
        size={{ base: "sm", md: "md" }}
        ml={3}
        onClick={handleConfirmStatusChange}
      >
        Yes
      </Button>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>

    </Layout>
  );
};

export default TopFacility;