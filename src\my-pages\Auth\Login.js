import { useState } from "react";
import {
  Flex,
  <PERSON>ing,
  Input,
  Button,
  InputGroup,
  Stack,
  InputLeftElement,
  chakra,
  Box,
  FormControl,
  InputRightElement,
  FormErrorMessage,
  useToast,
  Image,
} from "@chakra-ui/react";
import { FaUserAlt, FaLock } from "react-icons/fa";
import { useFormik } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { setCookie } from "../../utilities/auth";
import { useNavigate } from "react-router-dom";
import LogoImage from "../../assets/images/mask/MainKhelCoach.svg";
import { useDispatch } from "react-redux";
import { loginSuccess, loginFailure } from "./AuthSlice";

const CFaUserAlt = chakra(FaUserAlt);
const CFaLock = chakra(FaLock);

const Login = ({ renderMe }) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleShowClick = () => setShowPassword(!showPassword);

  const [isBtnLoading, setIsBtnLoading] = useState(false);

  const dispatch = useDispatch();

  const toast = useToast();
  const navigate = useNavigate();

  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .email("Enter a valid email")
      .required("Email is required"),
    password: Yup.string().required("Password is required"),
  });

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      setIsBtnLoading(true);
      let data = JSON.stringify({
        email: values.email,
        password: values.password,
      });

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `${process.env.REACT_APP_BASE_URL}/api/academy/login`,
        headers: {
          "Content-Type": "application/json",
        },
        data: data,
      };

      axios
        .request(config)
        .then((response) => {
          setIsBtnLoading(false);

          const resData = response.data;
          // Basic success check
          if (!(resData?.responseCode === 0 && resData?.status === "success")) {
            console.error("Login failed:", resData);
            return;
          }
          const userWrapper = resData?.data?.user;
          const user = userWrapper?.user || userWrapper || {};
          const accessToken = userWrapper?.accessToken;
          const refreshToken = userWrapper?.refreshToken; // if you need it

          if (!accessToken) {
            console.error("Login: missing accessToken in response:", resData);
            return;
          }

          // --- Persist session ----------------------------------------------------
          sessionStorage.setItem("admintoken", `Bearer ${accessToken}`);

          const academyId = user?.academyId?._id;
          if (academyId) {
            sessionStorage.setItem("academyId", academyId);
          }

          try {
            setCookie("userName", user?.name ?? "");
            setCookie("email", user?.email ?? "");
            if (user?.academyId?.name) {
              setCookie("academyName", user.academyId.name);
            }
          } catch (cookieErr) {
            console.warn("Cookie set failed:", cookieErr);
          }

          // --- Auth status redirect ----------------------------------------------
          const authStatus = user?.academyId?.authStatus?.toLowerCase?.();
          if (authStatus === "authorized") {
            navigate("/dashboard");
          } else {
            navigate("/verification-pending");
          }

          try {
            dispatch(
              loginSuccess({
                token: accessToken,
                user: {
                  _id: user?.id || user?._id,
                  name: user?.name,
                  email: user?.email,
                  academyId,
                  accessScopes: user?.accessScopes,
                  academyUserGroups: user?.academyUserGroups,
                },
              })
            );
          } catch (dispatchErr) {
            console.error("loginSuccess dispatch failed:", dispatchErr);
          }

          try {
            renderMe?.();
          } catch (renderErr) {
            console.error("renderMe() failed:", renderErr);
          }
          const displayName =
            user?.name ||
            user?.academyId?.name || // fallback to academy name
            user?.email ||
            "User";
        })
        .catch((error) => {
          setIsBtnLoading(false);

          // If axios cancellation
          if (axios.isCancel?.(error)) {
            console.warn("Login request canceled:", error?.message);
            return;
          }

        const res = error?.response?.data;
  const errMsg =
    res?.errors?.[0]?.message || // ✅ Pulls "Incorrect password"
    res?.message ||              // fallback
    error?.message ||            // fallback
    "Unable to log in. Please try again."; // final fallback

  // Optional: set field-level error if needed
  if (res?.errors?.[0]?.field === "password") {
    formik.setFieldError("password", res.errors[0].message);
  }
          toast({
            title: "Login failed",
            description: errMsg,
            status: "error",
            duration: 2000,
            isClosable: true,
            position: "top-center",
          });
        });
    },
  });

  return (
    <>
      <Flex
        flexDirection="column"
        width="100wh"
        height="100vh"
        backgroundColor="gray.200"
        justifyContent="center"
        alignItems="center"
      >
        <Stack
          flexDir="column"
          mb="2"
          justifyContent="center"
          alignItems="center"
        >
          <Image src={LogoImage} alt="logo" w={"200px"} mb={4} />
          <form onSubmit={formik.handleSubmit}>
            <Box minW={{ base: "90%", md: "468px" }}>
              <Stack
                spacing={4}
                p="1rem"
                backgroundColor="whiteAlpha.900"
                boxShadow="md"
              >
                <FormControl
                  isInvalid={formik.errors.email && formik.touched.email}
                >
                  <InputGroup>
                    <InputLeftElement
                      pointerEvents="none"
                      children={<CFaUserAlt color="gray.300" />}
                    />
                    <Input
                      type="email"
                      placeholder="Enter email"
                      id="email"
                      name="email"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.email}
                    />
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.email}</FormErrorMessage>
                </FormControl>
                <FormControl
                  isInvalid={formik.errors.password && formik.touched.password}
                >
                  <InputGroup>
                    <InputLeftElement
                      pointerEvents="none"
                      color="gray.300"
                      children={<CFaLock color="gray.300" />}
                    />
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter password"
                      id="password"
                      name="password"
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      value={formik.values.password}
                    />
                    <InputRightElement width="4.5rem">
                      <Button h="1.75rem" size="sm" onClick={handleShowClick}>
                        {showPassword ? "Hide" : "Show"}
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                  <FormErrorMessage>{formik.errors.password}</FormErrorMessage>
                </FormControl>
                <Flex justifyContent="space-between" alignItems="center" mb={2}>
                  <Box fontSize="sm">
                    Not a member?{" "}
                    <chakra.span
                      color="teal.500"
                      cursor="pointer"
                      onClick={() => navigate("/register")}
                    >
                      Sign Up
                    </chakra.span>
                  </Box>
                  <Box
                    fontSize="sm"
                    color="teal.500"
                    cursor="pointer"
                    onClick={() => navigate("/forgot-password")}
                  >
                    Forgot password?
                  </Box>
                </Flex>
                <Button
                  borderRadius={0}
                  type="submit"
                  variant="solid"
                  colorScheme="teal"
                  width="full"
                  isLoading={isBtnLoading}
                >
                  Login
                </Button>
              </Stack>
            </Box>
          </form>
        </Stack>
      </Flex>
    </>
  );
};

export default Login;
