import {
  Box,
  Text,
  Button,
  Flex,
  Input,
  Select,
  Stack,
  Spinner,
  InputGroup,
  InputRightAddon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import { MdDownload } from "react-icons/md";
import ReactPaginate from "react-paginate";
import { IoMdSearch } from "react-icons/io";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import { useNavigate } from "react-router-dom";

export default function ReportMobileView({
  data,
  isLoading,
  startDate,
  endDate,
  minEndDate,
  maxEndDate,
  setStartDate,
  setEndDate,
  searchCourseName,
  setSearchCourseName,
  selectedStatus,
  setSelectedStatus,
  onSearch,
  onDownload,
  onCoachSearch,
  totalPages,
  currentPage,
  handlePageChange,
}) {
  const navigate = useNavigate();
  return (
    <Box px={{base: 0, md: 1}} py={2}>
      <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
          className="p-0"
        >
        </Button>
        <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Reports</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
      </Flex>
      {/* Date Range Row */}
      <Flex direction="row" gap={2} mb={2} alignItems="flex-end" justifyContent="center">
        <Box flex="1" minW={0} textAlign="center">
          <Text fontSize="sm" mb={0}>Start Date</Text>
          <Input
            type="date"
            value={startDate}
            max={maxEndDate}
            onChange={e => setStartDate(e.target.value)}
            size="sm"
          />
        </Box>
        <Box alignSelf="center" px={1}>
          <Text fontSize="xl" color="gray.500">-</Text>
        </Box>
        <Box flex="1" minW={0} textAlign="center">
          <Text fontSize="sm" mb={0}>End Date</Text>
          <Input
            type="date"
            value={endDate}
            min={minEndDate}
            max={maxEndDate}
            onChange={e => setEndDate(e.target.value)}
            size="sm"
          />
        </Box>
      </Flex>
      {/* Second line: Search and other buttons */}
      <Flex direction="row" gap={2} mb={4} alignItems="center" justifyContent="center">
        <Button
          variant={"outline"}
          colorScheme="teal"
          size={"sm"}
          px={4}
          onClick={onCoachSearch}
          flex="2"
        >
          Search
        </Button>
        <Select
          placeholder="Status"
          value={selectedStatus}
          onChange={e => setSelectedStatus(e.target.value)}
          flex="1"
          size="sm"
        >
          <option value="">All</option>
          <option value="paid">Paid</option>
          <option value="unpaid">Unpaid</option>
        </Select>
        <Button
          onClick={onDownload}
        >
          Download
        </Button>
      </Flex>
      {/* Report Cards List */}
      <Stack spacing={4}>
        {isLoading ? (
          <Flex justify="center" align="center" py={10}>
            <Spinner />
          </Flex>
        ) : data && data.length > 0 ? (
          data.map((report, idx) => (
            <Box key={idx} border="1px solid #CBD5E0" borderRadius="md" p={3} bg="white">
              <Flex justify="space-between" alignItems="center" mb={2}>
                <Text fontWeight="bold">{report.bookingId}</Text>
                <Text fontSize="sm" color="gray.500">{report.date?.split("T")[0]}</Text>
              </Flex>
              <Text fontSize="sm" mb={1}><b>Course:</b> {report.courseName}</Text>
              <Text fontSize="sm" mb={1}><b>Coach:</b> {report.coachName}</Text>
              <Text fontSize="sm" mb={1}><b>Status:</b> {report.paymentStatus}</Text>
              <Text fontSize="sm" mb={1}><b>Amount:</b> ₹{report.amountReceived?.toFixed(2)}</Text>
            </Box>
          ))
        ) : (
          <Text color="gray.500" textAlign="center" py={6}>
            No report details to show
          </Text>
        )}
      </Stack>
      {/* Pagination */}
      {totalPages > 1 && (
        <Flex justify="center" align="center" mt={4}>
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}
    </Box>
  );
}
