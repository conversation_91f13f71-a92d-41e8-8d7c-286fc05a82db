import {
  Box,
  Text,
  Badge,
  Flex,
  Card,
  CardBody,
  Stack,
  Button,
  Spinner,
  Input,
  InputGroup,
  InputRightAddon,
  Select,
  Tooltip,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverCloseButton,
  PopoverBody,
  PopoverHeader,
  FormControl,
  FormLabel,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Icon,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
} from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";
import { IoIosClose } from "react-icons/io";
import { IoMdSearch } from "react-icons/io";
import { FaRupeeSign, FaCheckCircle, FaClock } from "react-icons/fa";
import ReactPaginate from "react-paginate";
import { useState } from "react";
import { IoArrowBackCircleOutline } from "react-icons/io5";

export default function BookingListMobileView({
  data,
  isLoading,
  formatDate,
  searchCourseName,
  setSearchCourseName,
  selectedClassType,
  setSelectedClassType,
  selectedStatus,
  setSelectedStatus,
  startDate,
  endDate,
  setStartDate,
  setEndDate,
  filters,
  setFilters,
  totalPages,
  currentPage,
  setCurrentPage,
  handlePageChange,
  showSearch,
  setShowSearch,
  paymentStats, // Add this prop
}) {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [startDateError, setStartDateError] = useState(false);
  const [endDateError, setEndDateError] = useState(false);

  return (
    <Box px={{base: 0, md: 1}} py={2}>
      <Flex alignItems="center" gap={0} mb={3}>
        <Button
          variant="ghost"
          size={{ base: "xs", md: "sm" }}
          leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
          onClick={() => navigate(-1)}
          _hover={{ bg: "gray.100" }}
          color="gray.700"
          fontWeight="bold"
          className="p-0"
        >
        </Button>
        <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink href="#">Booking</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>
      </Flex>
      {/* Payment Stats Cards for Mobile */}
      <SimpleGrid columns={1} spacing={3} mb={4}>
        <Card bg="white" shadow="sm" borderRadius="md" overflow="hidden">
          <CardBody p={3}>
            <Flex align="center" justify="space-between">
              <Stat>
                <StatLabel color="gray.600" fontSize="xs" fontWeight="medium">
                  Total Payment
                </StatLabel>
                <StatNumber fontSize="md" fontWeight="bold" color="blue.600">
                  ₹{paymentStats?.totalPayment?.toFixed(2) || "0.00"}
                </StatNumber>
                <StatHelpText color="gray.500" fontSize="xs" mb={0}>
                  Overall revenue
                </StatHelpText>
              </Stat>
              <Box p={2} bg="blue.50" borderRadius="md" color="blue.500">
                <Icon as={FaRupeeSign} boxSize={3} />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg="white" shadow="sm" borderRadius="md" overflow="hidden">
          <CardBody p={3}>
            <Flex align="center" justify="space-between">
              <Stat>
                <StatLabel color="gray.600" fontSize="xs" fontWeight="medium">
                  Payment Received
                </StatLabel>
                <StatNumber fontSize="md" fontWeight="bold" color="green.600">
                  ₹{paymentStats?.paymentReceived?.toFixed(2) || "0.00"}
                </StatNumber>
                <StatHelpText color="gray.500" fontSize="xs" mb={0}>
                  Successfully collected
                </StatHelpText>
              </Stat>
              <Box p={2} bg="green.50" borderRadius="md" color="green.500">
                <Icon as={FaCheckCircle} boxSize={3} />
              </Box>
            </Flex>
          </CardBody>
        </Card>

        <Card bg="white" shadow="sm" borderRadius="md" overflow="hidden">
          <CardBody p={3}>
            <Flex align="center" justify="space-between">
              <Stat>
                <StatLabel color="gray.600" fontSize="xs" fontWeight="medium">
                  Payment Pending
                </StatLabel>
                <StatNumber fontSize="md" fontWeight="bold" color="orange.600">
                  ₹{paymentStats?.paymentPending?.toFixed(2) || "0.00"}
                </StatNumber>
                <StatHelpText color="gray.500" fontSize="xs" mb={0}>
                  Awaiting payment
                </StatHelpText>
              </Stat>
              <Box p={2} bg="orange.50" borderRadius="md" color="orange.500">
                <Icon as={FaClock} boxSize={3} />
              </Box>
            </Flex>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Search */}
      <InputGroup mb={3}>
        <Input
          placeholder="Search by training schedule name"
          value={searchCourseName}
          onChange={(e) => {
            setSearchCourseName(e.target.value);
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              if (e.target.value.length >= 3) {
                setSearchCourseName(e.target.value);
              }
            }
          }}
        />
        {(searchCourseName && searchCourseName.length > 0) && (
          <InputRightAddon
            bgColor={"gray.300"}
            border={"1px"}
            borderColor={"gray.300"}
            onClick={() => setSearchCourseName("")}
            cursor={"pointer"}
          >
            <IoIosClose fontSize={"24px"} />
          </InputRightAddon>
        )}
      </InputGroup>

      {/* Filters: Date Range, Class Type, Status all in one line */}
      <Flex gap={2} mb={4} direction="row" flexWrap="nowrap" alignItems="center">
        {/* Date Range Filter (Popover) */}
        <Box flex="1" minW={0} position="relative">
          <Popover isOpen={isOpen} onClose={() => setIsOpen(false)}>
            <PopoverTrigger>
              <Text
                display="flex"
                justifyContent="center"
                alignItems="center"
                mb={0}
                position="relative"
                py={"3px"}
                border={"1px"}
                borderColor={"gray.300"}
                rounded={"md"}
                color="gray.700"
                cursor={"pointer"}
                textAlign={"center"}
                bgColor={filters.startDate && filters.endDate && "gray.300"}
                onClick={() => {
                  setIsOpen(true);
                  setStartDateError(false);
                  setEndDateError(false);
                }}
              >
                Date Range
              </Text>
            </PopoverTrigger>
            <PopoverContent border={"1px"} borderColor={"gray.300"}>
              <PopoverArrow />
              <PopoverCloseButton
                mt={1}
                onClick={() => {
                  setIsOpen(false);
                  setStartDateError(false);
                  setEndDateError(false);
                }}
              />
              <PopoverHeader fontWeight={"semibold"}>Select Date Range</PopoverHeader>
              <PopoverBody zIndex={"99"} bgColor={"F5F5F5"}>
                <Box mb={2}>
                  <FormControl isInvalid={startDateError}>
                    <FormLabel>Start Date</FormLabel>
                    <InputGroup size={"md"}>
                      <Input
                        type="date"
                        max={filters.endDate || new Date().toISOString().split("T")[0]}
                        value={filters.startDate}
                        name="startDate"
                        onChange={e => {
                          setStartDateError(false);
                          setFilters(prev => ({ ...prev, startDate: e.target.value }));
                        }}
                      />
                      <InputRightAddon
                        bgColor={"gray.300"}
                        border={"1px"}
                        borderColor={"gray.300"}
                        cursor={"pointer"}
                        onClick={() => {
                          setFilters(prev => ({ ...prev, startDate: "" }));
                        }}
                      >
                        <IoIosClose fontSize={"24px"} />
                      </InputRightAddon>
                    </InputGroup>
                    {startDateError && (
                      <Text color={"red.500"} fontSize={"0.9rem"}>
                        Enter start date
                      </Text>
                    )}
                  </FormControl>
                  <FormControl mt={2} isInvalid={endDateError}>
                    <FormLabel>End Date</FormLabel>
                    <InputGroup size={"md"}>
                      <Input
                        type="date"
                        min={filters.startDate}
                        max={new Date().toISOString().split("T")[0]}
                        value={filters.endDate}
                        name="endDate"
                        onChange={e => {
                          setEndDateError(false);
                          setFilters(prev => ({ ...prev, endDate: e.target.value }));
                        }}
                      />
                      <InputRightAddon
                        bgColor={"gray.300"}
                        border={"1px"}
                        borderColor={"gray.300"}
                        cursor={"pointer"}
                        onClick={() => {
                          setFilters(prev => ({ ...prev, endDate: "" }));
                        }}
                      >
                        <IoIosClose fontSize={"24px"} />
                      </InputRightAddon>
                    </InputGroup>
                    {endDateError && (
                      <Text color={"red.500"} fontSize={"0.9rem"}>
                        Enter end date
                      </Text>
                    )}
                  </FormControl>
                  <Button
                    colorScheme="telegram"
                    size={"sm"}
                    w={"full"}
                    mt={4}
                    isDisabled={!filters.startDate && !filters.endDate}
                    onClick={() => {
                      if (!filters.startDate) {
                        setStartDateError(true);
                        return;
                      }
                      if (!filters.endDate) {
                        setEndDateError(true);
                        return;
                      }
                      setIsOpen(false);
                      setStartDate(filters.startDate);
                      setEndDate(filters.endDate);
                    }}
                  >
                    Apply
                  </Button>
                </Box>
              </PopoverBody>
            </PopoverContent>
          </Popover>
          {/* Badge to clear filter */}
          {(filters.startDate || filters.endDate) && (
            <Tooltip label={"Remove filter"}>
              <Box
                position="absolute"
                top={-1}
                right={-1}
                bgColor="gray.600"
                borderRadius="full"
                p={1}
                cursor={"pointer"}
                onClick={() => {
                  setFilters({ startDate: "", endDate: "" });
                  setStartDate("");
                  setEndDate("");
                }}
              >
                <IoIosClose fontSize={"10px"} color="white" />
              </Box>
            </Tooltip>
          )}
        </Box>
        <Select
          placeholder="Class Type"
          value={selectedClassType}
          borderColor={"gray.300"}
          bgColor={selectedClassType && "gray.300"}
          cursor={"pointer"}
          onChange={(e) => {
            setSelectedClassType(e.target.value);
            setCurrentPage(1);
          }}
          flex="1"
          minW="0"
          size="sm"
        >
          <option value="class">Class</option>
          <option value="course">Course</option>
          <option value="">All</option>
        </Select>
        <Select
          placeholder="Status"
          value={selectedStatus}
          borderColor={"gray.300"}
          bgColor={selectedStatus && "gray.300"}
          cursor={"pointer"}
          onChange={(e) => {
            setSelectedStatus(e.target.value);
            setCurrentPage(1);
          }}
          flex="1"
          minW="0"
          size="sm"
        >
          <option value="Pending">Active</option>
          <option value="Completed">Completed</option>
          <option value="">All</option>
        </Select>
      </Flex>

      {/* Booking Cards List */}
      <Stack spacing={4}>
        {isLoading ? (
          <Flex justify="center" align="center" py={10}>
            <Spinner />
          </Flex>
        ) : data && data.length > 0 ? (
          data.map((bookData) => (
            <Card key={bookData._id} border="1px solid #CBD5E0">
              <CardBody>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Booking Id:</Text>
                  <Text fontSize="sm">{bookData.bookingId}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Course:</Text>
                  <Text fontSize="sm">{bookData.courseName || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Customer:</Text>
                  <Text fontSize="sm">{bookData.playerName || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Type:</Text>
                  <Text fontSize="sm">{bookData.courseType?.charAt(0).toUpperCase() + bookData.courseType?.slice(1) || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Amount:</Text>
                  <Text fontSize="sm">₹{bookData.pricePaid?.toFixed(2) || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Date:</Text>
                  <Text fontSize="sm">{formatDate(bookData.createdAt) || "n/a"}</Text>
                </Flex>
                <Flex justify="space-between" align="center">
                  <Text fontSize="sm" color="gray.600" fontWeight="bold" mb={0}>Status:</Text>
                  <Badge colorScheme={bookData.status?.toLowerCase() === "active" ? "green" : "red"}>
                    {bookData.status}
                  </Badge>
                </Flex>
                <Flex justify="flex-end" mt={3}>
                  <Button
                    size="sm"
                    onClick={() => navigate(`/Booking/details/${bookData._id}`)}
                  >
                    View Details
                  </Button>
                </Flex>
              </CardBody>
            </Card>
          ))
        ) : (
          <Text color="gray.500" textAlign="center" py={6}>
            No booking details to show
          </Text>
        )}
      </Stack>

      {/* Pagination (match desktop) */}
      {totalPages > 1 && (
        <Flex justify="center" align="center" mt={4}>
          <ReactPaginate
            previousLabel="Previous"
            nextLabel="Next"
            breakLabel="..."
            pageCount={totalPages}
            marginPagesDisplayed={2}
            pageRangeDisplayed={5}
            onPageChange={handlePageChange}
            containerClassName="pagination"
            activeClassName="active"
            forcePage={currentPage - 1}
          />
        </Flex>
      )}
    </Box>
  );
}
