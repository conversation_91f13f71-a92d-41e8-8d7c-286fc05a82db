import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { useParams } from "react-router-dom";
import {
  Box,
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Card,
  CardBody,
  Stack,
  Text,
  Flex,
  TableContainer,
  Heading,
  StackDivider,
  Tooltip,
  Spinner,
  useToast,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  StatNumber,
  Stat,
  Badge,
  Tag,
  Button,
} from "@chakra-ui/react";
import { Link, useNavigate } from "react-router-dom";
import axios from "axios";
import { IoArrowBackCircleOutline } from "react-icons/io5";

const Bookingdetails = () => {
  const [bookingdetailData, setBookingdetailData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const paramsData = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];

  const bookingId = paramsData.id;

  useEffect(() => {
    fetchseprateData(bookingId);
  }, [bookingId]);

  const fetchseprateData = async (bookingId) => {
    setBookingdetailData({ result: [], isLoading: true, error: false });
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/booking/${bookingId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setBookingdetailData({
        result: [response.data],
        isLoading: false,
        error: false,
      });
      // console.log("received at detail",response.data)
    } catch (error) {
      console.error("Error fetching booking data:", error);
      if (error.response.status === 403) {
        toast({
          title: "You don't have an access to perform this action",
          status: "warning",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      } else {
        toast({
          title: "Something went wrong please try again later",
          status: "error",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      }
    }
  };

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Booking Details">
        <Flex justifyContent={"flex-start"} alignItems={"center"} mb={3}>
          <Button
            variant="ghost"
            size={{ base: "xs", md: "sm" }}
            leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
            onClick={() => navigate(-1)}
            _hover={{ bg: "gray.100" }}
            color="gray.700"
            fontWeight="bold"
            className="p-0"
          >
          </Button>
          <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
            <BreadcrumbItem>
              <Link to={"/Booking"} href="#">
                Bookings
              </Link>
            </BreadcrumbItem>

            <BreadcrumbItem isCurrentPage>
              <BreadcrumbLink
                href="#"
                maxWidth={{ base: "180px", sm: "200px", md: "none" }}
                overflow="hidden"
                textOverflow="ellipsis"
                whiteSpace="nowrap"
                display="inline-block"
                title={`Booking Details - ${bookingdetailData?.result?.[0]?.player?.firstName ? bookingdetailData.result[0].player.firstName + ' ' + bookingdetailData.result[0].player.lastName : bookingId}`}
              >
                {`Booking Details - ${bookingdetailData?.result?.[0]?.player?.firstName ? bookingdetailData.result[0].player.firstName + ' ' + bookingdetailData.result[0].player.lastName : bookingId}`}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </Breadcrumb>
        </Flex>

        {!bookingdetailData?.isLoading && bookingdetailData?.error ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Text color={"red.500"}>
              Something went wrong please try again later...
            </Text>
          </Flex>
        ) : bookingdetailData?.isLoading ? (
          <Flex
            justifyContent={"center"}
            alignItems={"center"}
            w={"full"}
            my={10}
          >
            <Spinner size={"xl"} />
          </Flex>
        ) : (
          <Card
            mt={4}
            height={`${window.innerHeight - 160}px`}
            overflowY={"scroll"}
          >
            <CardBody>
              {bookingdetailData?.result.map((booking, index) => (
                <Stack key={index} divider={<StackDivider />} spacing="4">
                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Booking Id
                    </Heading>
                    <Text pt="2" fontSize="md">
                      {booking?.bookingId}
                    </Text>
                  </Box>

                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Player Details
                    </Heading>
                    <Box>
                    <Flex flexDirection={'column'}>
                    <Text pt="2" fontSize="md" mb={0}>
                      {booking?.player?.firstName +
                        " " +
                        booking?.player?.lastName}
                    </Text>
                    <Text fontSize="md" mb={0}>
                      {booking?.player?.email || 'n/a'}
                    </Text>
                    <Text fontSize="md" mb={0}>
                      {booking?.player?.mobile || 'n/a'}
                    </Text>
                    </Flex>
                    </Box>
                  </Box>

                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Course Type
                    </Heading>
                    <Tag size="lg" variant="solid" colorScheme="teal" mt={1}>
                      {booking?.courseType.charAt(0).toUpperCase() +
                        booking?.courseType?.slice(1) || "n/a"}
                    </Tag>
                  </Box>

                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Booking Date
                    </Heading>
                    <Text pt="2" fontSize="md">
                      {new Date(booking?.bookingDate)?.toLocaleDateString() ||
                        "n/a"}
                    </Text>
                  </Box>

                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Booking Status
                    </Heading>
                    <Badge
                      fontSize={"sm"}
                      colorScheme={
                        booking?.status?.toLowerCase() === "pending"
                          ? "green"
                          : booking?.status?.toLowerCase() === "completed"
                          ? "purple"
                          : "red"
                      }
                    >
                      {booking?.status
                        ? booking?.status?.toLowerCase() === "pending"
                          ? "active"
                          : booking?.status
                        : "n/a"}
                    </Badge>
                  </Box>

                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Payment Status
                    </Heading>
                    <Text pt="2" fontSize="sm">
                      <Badge
                        fontSize={"sm"}
                        colorScheme={
                          booking?.paymentStatus?.toLowerCase() === "success"
                            ? "green"
                            : "red"
                        }
                      >
                        {booking?.paymentStatus || "n/a"}
                      </Badge>
                    </Text>
                  </Box>
                  {booking?.wallet && (
                    <Box>
                      <Heading size="xs" textTransform="uppercase">
                        Wallet Amount
                      </Heading>
                      <Stat>
                        <StatNumber fontSize={"large"}>
                          ₹{booking?.walletAmount?.toFixed(2) || "0.00"}
                        </StatNumber>
                      </Stat>
                    </Box>
                  )}
                  {booking?.paymentId &&
                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Payment Id
                    </Heading>
                    <Text pt="2" fontSize="sm">
                      {booking?.paymentId || "n/a"}
                    </Text>
                  </Box>
                  }
                  {booking?.razorPayPaymentId && 
                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Razor Pay Payment Id
                    </Heading>
                    <Text pt="2" fontSize="sm">
                      {booking?.razorPayPaymentId || "n/a"}
                    </Text>
                  </Box>
                  }
                  {booking?.paymentMode &&
                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Payment Mode
                    </Heading>
                    <Text
                      pt="2"
                      fontSize="md"
                      fontWeight={"semibold"}
                      letterSpacing={"0.01rem"}
                    >
                      {booking?.paymentMode?.toUpperCase() || "n/a"}
                    </Text>
                  </Box>
                  }
                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Price Paid
                    </Heading>
                    <Stat>
                      <StatNumber fontSize={"large"}>
                        ₹{booking?.pricePaid?.toFixed(2) || "n/a"}
                      </StatNumber>
                    </Stat>
                  </Box>

                  <Box>
                    <Heading size="xs" textTransform="uppercase">
                      Classes Detail
                    </Heading>

                    <TableContainer mt={6}>
                      <Table
                        variant="simple"
                        border={"1px"}
                        borderColor={"#E2DFDF"}
                      >
                        <Thead bgColor={"#E2DFDF"}>
                          <Tr>
                            <Th fontSize={"11px"}>Start Date</Th>
                            <Th fontSize={"11px"}>Start Time</Th>
                            <Th fontSize={"11px"}>End Time</Th>
                            <Th fontSize={"11px"}>Status</Th>
                            <Th fontSize={"11px"}>Duration</Th>
                            <Th fontSize={"11px"}>Fees</Th>
                            <Th fontSize={"11px"}>Days</Th>
                            <Th fontSize={"11px"}>Attendance</Th>
                          </Tr>
                        </Thead>
                        <Tbody>
                          {booking?.classes?.map((date, idx) => (
                            <Tr key={idx} fontSize={"14px"}>
                              <Td>
                                {new Date(date?.date)?.toLocaleDateString() ||
                                  "n/a"}
                              </Td>
                              <Td>{date?.startTime || "n/a"}</Td>
                              <Td>{date?.endTime || "n/a"}</Td>
                              <Td>
                                <Badge
                                  colorScheme={
                                    date?.status?.toLowerCase() === "pending"
                                      ? "teal"
                                      : date?.status?.toLowerCase() ===
                                        "completed"
                                      ? "green"
                                      : date?.status?.toLowerCase() ===
                                        "in progress"
                                      ? "yellow"
                                      : date?.status?.toLowerCase() ===
                                        "upcoming"
                                      ? "blue"
                                      : date?.status?.toLowerCase() ===
                                        "cancelled"
                                      ? "red"
                                      : "gray"
                                  }
                                >
                                  {date?.status || "n/a"}
                                </Badge>
                              </Td>
                              <Td>{date?.duration || "n/a"}</Td>
                              <Td>₹{date?.fees?.toFixed(2) || "n/a"}</Td>
                              <Td>{date?.days || "n/a"}</Td>
                              <Td textAlign={"center"}>{date?.attendance}</Td>
                            </Tr>
                          ))}
                        </Tbody>
                      </Table>
                    </TableContainer>
                  </Box>
                </Stack>
              ))}
            </CardBody>
          </Card>
        )}
      </Layout>
    </Box>
  );
};

export default Bookingdetails;
