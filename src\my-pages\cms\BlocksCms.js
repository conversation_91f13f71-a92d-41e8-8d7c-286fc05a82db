import React, { useEffect, useState } from "react";
import { useMediaQuery } from "@chakra-ui/react";
import Layout from "../../layout/default";
import { MdEdit, MdDragHandle } from "react-icons/md";
import axios from "axios";

import {
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Text,
  Flex,
  Tooltip,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  Button,
  Spinner,
  useToast,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
  FormControl,
  FormLabel,
  Input,
  Select,
  Box,
  NumberInput,
  NumberInputField,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import EditBlockModal from "./EditBlockModal";
import { getCookie } from "../../utilities/auth";
import { useSelector } from "react-redux";
import { jwtDecode } from "jwt-decode";

const BlocksCms = () => {
  const navigate = useNavigate();
  const [blockData, setBlockData] = useState({
    result: [],
    isLoading: false,
    error: false,
  });
  const [adjustBtnEdit, setAdjustBtnEdit] = useState(false);
  const [prevBlockData, setPrevBlockData] = useState([]);
  const [posBtnLoading, setPosBtnLoading] = useState(false);

  const [draggedItemIndex, setDraggedItemIndex] = useState(null);
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  const [viewBlock, setViewBlock] = useState({
    identity: "",
    title: "",
    visibility: "",
    position: null,
    // min: null,
    max: null,
  });
  const [blockId, setBlockId] = useState("");
  const [renderMe, setRenderMe] = useState(0);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [isOpen1, setIsOpen1] = useState(false);
  const onClose1 = () => setIsOpen1(false);
  const onOpen1 = () => setIsOpen1(true);

  const [isOpen2, setIsOpen2] = useState(false);
  const onClose2 = () => setIsOpen2(false);
  const onOpen2 = () => setIsOpen2(true);

  const [createBlockData, setCreateBlockData] = useState({
    identity: "",
    title: "",
    visibility: true,
    position: "",
    max: "",
    collectionName: ""
  });
  const [isCreateLoading, setIsCreateLoading] = useState(false);
  const [createBlockErrors, setCreateBlockErrors] = useState({});

  const toast = useToast();
  const userData = useSelector((state) => state.user);
  const [isMobile] = useMediaQuery("(max-width: 768px)");

  const getBlocks = () => {
    const decodedToken = jwtDecode(token);
    const academyId = decodedToken.academyId._id;

    setBlockData({ result: [], isLoading: true, error: false });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/admin/blocks`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        // Use the new structure directly
        const blocks = response.data.data;
        setBlockData({
          result: blocks.sort((a, b) => a.position - b.position),
          isLoading: false,
          error: false,
        });
      })
      .catch((error) => {
        console.log(error);
        const errorMessage = error.response?.data?.message || error.response?.data?.error || "Something went wrong please try again later";
        setBlockData({ result: [], isLoading: false, error: errorMessage });
        if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: errorMessage,
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const renderMeFn = () => {
    setRenderMe((prev) => prev + 1);
  };

  const handleDragStart = (index) => {
    setDraggedItemIndex(index);
  };

  const handleDragOver = (index) => {
    if (draggedItemIndex === null) return;
    const newItems = [...blockData.result]; // Copy the result array from blockData
    const draggedItem = newItems[draggedItemIndex];
    newItems.splice(draggedItemIndex, 1);
    newItems.splice(index, 0, draggedItem);

    // Adjust positions of items inside newItems array
    newItems.forEach((item, idx) => {
      item.position = idx + 1;
    });

    setBlockData({ ...blockData, result: newItems }); // Update result array in blockData
    setDraggedItemIndex(index);
  };

  const handleDragEnd = () => {
    setDraggedItemIndex(null);
  };

  const updateBlockPosition = () => {
    setPosBtnLoading(true);
    const value = blockData.result.map((x) => {
      return { id: x._id, newPosition: x.position };
    });
    let data = JSON.stringify({
      updates: value,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/update/block/position`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setPosBtnLoading(false);
        setPrevBlockData([]);
        setAdjustBtnEdit(false);
        onClose1();
        toast({
          title: "Block position updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        onClose1();
        setPosBtnLoading(false);
        setPrevBlockData([]);
        setAdjustBtnEdit(false);
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const createBlock = () => {
    // Client-side validation
    const errors = {};

    if (!createBlockData.identity || createBlockData.identity.trim() === "") {
      errors.identity = "Identity is required";
    }

    if (!createBlockData.title || createBlockData.title.trim() === "") {
      errors.title = "Title is required";
    }

    if (!createBlockData.position || createBlockData.position === "") {
      errors.position = "Position is required";
    }

    if (!createBlockData.max || createBlockData.max === "") {
      errors.max = "Max is required";
    }

    if (!createBlockData.collectionName || createBlockData.collectionName === "") {
      errors.collectionName = "Collection Name is required";
    }

    // If there are validation errors, show them and don't submit
    if (Object.keys(errors).length > 0) {
      setCreateBlockErrors(errors);
      return;
    }

    setIsCreateLoading(true);
    setCreateBlockErrors({});
    let data = JSON.stringify(createBlockData);
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/academy-cms/create/block`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        onClose2();
        setIsCreateLoading(false);
        setCreateBlockErrors({});
        renderMeFn();
        setCreateBlockData({
          identity: "",
          title: "",
          visibility: true,
          position: "",
          max: "",
          collectionName: ""
        });
        toast({
          title: "Block created successfully",
          status: "success",
          duration: 3500,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        setIsCreateLoading(false);
        if (error.response?.data?.errors) {
          // Map errors to field: message
          const fieldErrors = {};
          error.response.data.errors.forEach(err => {
            fieldErrors[err.field] = err.message;
          });
          setCreateBlockErrors(fieldErrors);
        } else if (error.response?.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    getBlocks();
  }, [renderMe]);

  // Compute available collection names for dropdown
  const allCollectionOptions = [
    { value: "academyFacilities", label: "Facilities" },
    { value: "academyTopCoachCms", label: "Coaches" },
    { value: "academyTopCourseCms", label: "Training Schedules" },
    { value: "academyTestimonial", label: "Testimonials" },
    { value: "academyDescription", label: "Description" },
  ];
  const usedCollectionNames = blockData.result.map(b => b.collectionName);
  const availableCollectionOptions = allCollectionOptions.filter(opt => !usedCollectionNames.includes(opt.value));

  return (
    <Layout title="CMS | Blocks">
      {/* Responsive Back Button, Breadcrumb, and Action Buttons */}
      {isMobile ? (
        <Flex direction="column" alignItems="flex-start" gap={1} mb={3}>
          <Flex alignItems="center" gap={0} mb={{base: 3, md: 0}}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Blocks</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.cms?.includes("write") && !adjustBtnEdit && (
            <Flex direction="row" gap={2} w="100%">
              <Button
                variant={"outline"}
                colorScheme="telegram"
                size="xs"
                py={3}
                px={3}
                w="auto"
                fontSize="xs"
                onClick={() => {
                  setPrevBlockData(blockData.result);
                  setAdjustBtnEdit(true);
                }}
                mr={1}
                mb={0}
                disabled={adjustBtnEdit}
              >
                Adjust Position
              </Button>
              <Button
                variant={"outline"}
                colorScheme="teal"
                size="xs"
                py={3}
                px={3}
                w="auto"
                fontSize="xs"
                onClick={onOpen2}
                mr={0}
                mb={0}
                isDisabled={false}
              >
                Add Block
              </Button>
            </Flex>
          )}
          {adjustBtnEdit && (
            <Flex direction="row" gap={2} w="100%">
              <Button
                variant={"outline"}
                colorScheme="red"
                size="xs"
                py={3}
                px={3}
                w="auto"
                fontSize="xs"
                onClick={() => {
                  setBlockData({
                    result: prevBlockData,
                    isLoading: false,
                    error: false,
                  });
                  setAdjustBtnEdit(false);
                }}
                mr={1}
                mb={0}
              >
                Discard
              </Button>
              <Button
                variant={"outline"}
                colorScheme="green"
                size="xs"
                py={3}
                px={3}
                w="auto"
                fontSize="xs"
                isLoading={posBtnLoading}
                onClick={onOpen1}
                mr={0}
                mb={0}
              >
                Save Changes
              </Button>
            </Flex>
          )}
        </Flex>
      ) : (
        <Flex justifyContent={{ base: "flex-start", md: "space-between" }} alignItems={{ base: "flex-start", md: "center" }} direction={{ base: "column", md: "row" }} mb={{ base: 2, md: 4 }}>
          <Flex alignItems="center" gap={0}>
            <Button
              variant="ghost"
              size={{ base: "xs", md: "sm" }}
              leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
              onClick={() => navigate(-1)}
              _hover={{ bg: "gray.100" }}
              color="gray.700"
              fontWeight="bold"
              className="p-0"
            >
            </Button>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">CMS</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">Blocks</BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          <Box mt={{ base: 2, md: 0 }}>
            {!adjustBtnEdit ? (
              userData?.accessScopes?.cms?.includes("write") && (
                <Flex direction="row" gap={2}>
                  <Button
                    variant={"outline"}
                    colorScheme="telegram"
                    size={{ base: "xs", md: "sm" }}
                    py={{ base: 2, md: 5 }}
                    px={{ base: 2, md: 4 }}
                    onClick={() => {
                      setPrevBlockData(blockData.result);
                      setAdjustBtnEdit(true);
                    }}
                    flexShrink={0}
                  >
                    Adjust Position
                  </Button>
                  <Button
                    variant={"outline"}
                    colorScheme="teal"
                    size={{ base: "xs", md: "sm" }}
                    py={{ base: 2, md: 5 }}
                    px={{ base: 2, md: 4 }}
                    onClick={onOpen2}
                    isDisabled={false}
                    flexShrink={0}
                  >
                    Add Block
                  </Button>
                </Flex>
              )
            ) : (
              <Flex wrap="nowrap" gap={2}>
                <Button
                  variant={"outline"}
                  colorScheme="red"
                  size={{ base: "xs", md: "sm" }}
                  py={{ base: 2, md: 5 }}
                  px={{ base: 2, md: 4 }}
                  onClick={() => {
                    setBlockData({
                      result: prevBlockData,
                      isLoading: false,
                      error: false,
                    });
                    setAdjustBtnEdit(false);
                  }}
                  flexShrink={0}
                >
                  Discard
                </Button>
                <Button
                  variant={"outline"}
                  colorScheme="green"
                  size={{ base: "xs", md: "sm" }}
                  py={{ base: 2, md: 5 }}
                  px={{ base: 2, md: 4 }}
                  isLoading={posBtnLoading}
                  onClick={onOpen1}
                  flexShrink={0}
                >
                  Save Changes
                </Button>
              </Flex>
            )}
          </Box>
        </Flex>
      )}
      {/* Block List: Card view on mobile, table on desktop */}
      {isMobile ? (
        !blockData?.isLoading && blockData?.error ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={10}>
            <Text color="red.500">{blockData?.error}</Text>
          </Flex>
        ) : blockData?.isLoading ? (
          <Flex justifyContent="center" alignItems="center" w="full" my={6}>
            <Spinner size="md" />
          </Flex>
        ) : (
          <Box mt={2}>
            {blockData?.result?.map((block, inx) => (
              <Box
                key={block._id}
                borderWidth="1px"
                borderRadius="lg"
                boxShadow="sm"
                p={4}
                mb={3}
                bg="white"
              >
                {/* Identity */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Identity:</Text>
                  <Text fontSize="sm">{block?.identity || "n/a"}</Text>
                </Flex>
                {/* Title */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Title:</Text>
                  <Text fontSize="sm">{block?.title || "n/a"}</Text>
                </Flex>
                {/* Visibility */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Visibility:</Text>
                  <Text fontSize="sm">{block?.visibility ? "Visible" : "Not Visible"}</Text>
                </Flex>
                {/* Max */}
                <Flex justify="space-between" align="center" mb="10px">
                  <Text fontWeight="semibold" fontSize="sm" mb={0}>Max:</Text>
                  <Text fontSize="sm">{(block?.identity?.toLowerCase() !== "registration") && (block?.max || "--")}</Text>
                </Flex>
                {/* Collection */}
                <Flex justify="space-between" align="center">
                  <Text fontWeight="semibold" fontSize="sm" mb={0} >Collection:</Text>
                  <Text fontSize="sm">{block?.collectionName || "--"}</Text>
                </Flex>
                <Flex justifyContent="flex-end" gap={3} mt={2}>
                  {userData?.accessScopes?.cms?.includes("write") && (
                    <Tooltip label="Edit Block">
                      <Flex
                        color={"black.500"}
                        justifyContent="flex-start"
                        alignItems="center"
                        cursor={"pointer"}
                        onClick={() => {
                          onOpen();
                          setBlockId(block._id);
                          setViewBlock({
                            identity: block?.identity,
                            title: block?.title || "",
                            visibility: block?.visibility,
                            max: block?.max,
                            position: Number(block?.position),
                            collectionName: block?.collectionName,
                          });
                        }}
                      >
                        <MdEdit fontSize="20px" />
                      </Flex>
                    </Tooltip>
                  )}
                  {adjustBtnEdit && (
                    <Text as="span" ml={2}>
                      <MdDragHandle style={{ cursor: "grab" }} fontSize="24px" />
                    </Text>
                  )}
                </Flex>
              </Box>
            ))}
          </Box>
        )
      ) : (
        !blockData?.isLoading && blockData?.error ? (
          <Flex
            justifyContent={{ base: "flex-start", md: "center" }}
            alignItems={{ base: "flex-start", md: "center" }}
            w={"full"}
            my={{ base: 4, md: 10 }}
            px={{ base: 2, md: 0 }}
          >
            <Text color={"red.500"} fontSize={{ base: "sm", md: "md" }}>
              {blockData?.error}
            </Text>
          </Flex>
        ) : (
          <TableContainer
            mt={{ base: 2, md: 6 }}
            height={{ base: "auto", md: `${window.innerHeight - 185}px` }}
            maxHeight={{ base: "60vh", md: `${window.innerHeight - 185}px` }}
            overflowY={"auto"}
            px={{ base: 1, md: 0 }}
          >
            <Table variant="simple" size={{ base: "sm", md: "md" }}>
              <Thead
                bgColor={"#c1eaee"}
                position={"sticky"}
                top={"0px"}
                zIndex={"99"}
              >
                <Tr bgColor={"#E2DFDF"}>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Position</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Identity</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Title</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Visibility</Th>
                  <Th fontSize={{ base: "xs", md: "xs" }}>Max</Th>
                  {/* <Th>Min</Th> */}
                  <Th textAlign={{ base: "left", md: "center" }} fontSize={{ base: "xs", md: "md" }}>Action</Th>
                </Tr>
              </Thead>
              <Tbody>
                {blockData?.isLoading && !blockData?.error ? (
                  <Tr>
                    <Td></Td>
                    <Td></Td>
                    <Td
                      display={{ base: "block", md: "flex" }}
                      justifyContent={{ base: "flex-start", md: "center" }}
                      alignItems={{ base: "flex-start", md: "center" }}
                    >
                      <Spinner size={{ base: "xs", md: "md" }} />
                    </Td>
                    <Td></Td>
                    <Td></Td>
                    <Td></Td>
                  </Tr>
                ) : (
                  blockData?.result?.map((blockData, inx) => {
                    return (
                      <>
                        {adjustBtnEdit ? (
                          <Tr
                            key={blockData._id}
                            draggable
                            onDragStart={() => handleDragStart(inx)}
                            onDragOver={() => handleDragOver(inx)}
                            onDragEnd={handleDragEnd}
                          >
                            <Td fontSize={{ base: "xs", md: "14px" }}>{inx + 1 + "."}</Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>{blockData?.identity}</Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              fontSize={{ base: "xs", md: "14px" }}
                            >
                              {blockData?.title}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {blockData?.visibility ? "Visible" : "Not Visible"}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>{(blockData?.identity?.toLowerCase() !== "registration") && blockData?.max || "--"}</Td>
                            {/* <Td>{blockData?.min || "--"}</Td> */}
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {userData?.accessScopes?.cms?.includes("write") && (
                                <Flex>
                                  {!adjustBtnEdit ? (
                                    <Tooltip label="Edit Block">
                                      <Flex
                                        color={"black.500"}
                                        justifyContent={{ base: "flex-start", md: "center" }}
                                        alignItems={{ base: "flex-start", md: "center" }}
                                        cursor={"pointer"}
                                        onClick={() => {
                                          onOpen();
                                          setBlockId(blockData._id);
                                          setViewBlock({
                                            identity: blockData?.identity,
                                            title: blockData?.title || "",
                                            visibility: blockData?.visibility,
                                            // min: blockData?.min,
                                            max: blockData?.max,
                                            position: Number(blockData?.position),
                                            collectionName: blockData?.collectionName,
                                          });
                                        }}
                                      >
                                        <MdEdit fontSize={{ base: "lg", md: "24px" }} />
                                      </Flex>
                                    </Tooltip>
                                  ) : (
                                    <Text as={"span"} ml={{ base: 1, md: 3 }}>
                                      <MdDragHandle
                                        style={{ cursor: "grab" }}
                                        fontSize={{ base: "lg", md: "24px" }}
                                      />
                                    </Text>
                                  )}
                                </Flex>
                              )}
                            </Td>
                          </Tr>
                        ) : (
                          <Tr key={blockData._id}>
                            <Td fontSize={{ base: "xs", md: "14px" }}>{inx + 1 + "."}</Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>{blockData?.identity}</Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              fontSize={{ base: "xs", md: "14px" }}
                            >
                              {blockData?.title}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {blockData?.visibility ? "Visible" : "Not Visible"}
                            </Td>
                            <Td fontSize={{ base: "xs", md: "14px" }}>{(blockData?.identity?.toLowerCase() !== "registration") && (blockData?.max || "--")}</Td>
                            {/* <Td>{blockData?.min || "--"}</Td> */}
                            <Td fontSize={{ base: "xs", md: "14px" }}>
                              {userData?.accessScopes?.cms?.includes("write") && (
                                <Flex>
                                  {!adjustBtnEdit ? (
                                    <Tooltip label="Edit Block">
                                      <Flex
                                        color={"black.500"}
                                        justifyContent={{ base: "flex-start", md: "center" }}
                                        alignItems={{ base: "flex-start", md: "center" }}
                                        cursor={"pointer"}
                                        onClick={() => {
                                          onOpen();
                                          setBlockId(blockData._id);
                                          setViewBlock({
                                            identity: blockData?.identity,
                                            title: blockData?.title || "",
                                            visibility: blockData?.visibility,
                                            // min: blockData?.min,
                                            max: blockData?.max,
                                            position: Number(blockData?.position),
                                            collectionName: blockData?.collectionName,
                                          });
                                        }}
                                      >
                                        <MdEdit fontSize={{ base: "lg", md: "24px" }} />
                                      </Flex>
                                    </Tooltip>
                                  ) : (
                                    <Text as={"span"} ml={{ base: 1, md: 3 }}>
                                      <MdDragHandle
                                        style={{ cursor: "grab" }}
                                        fontSize={{ base: "lg", md: "24px" }}
                                      />
                                    </Text>
                                  )}
                                </Flex>
                              )}
                            </Td>
                          </Tr>
                        )}
                      </>
                    );
                  })
                )}
              </Tbody>
            </Table>
          </TableContainer>
        )
      )}

      {/* Modal for course search */}
      <Modal isOpen={isOpen} onClose={onClose} size={{ base: "sm", md: "xl" }} isCentered>
        <ModalOverlay />
        <ModalContent mx={{ base: 4, md: 0 }} my={{ base: 8, md: "auto" }}>
          <ModalHeader fontSize={{ base: "lg", md: "xl" }}>Edit Block</ModalHeader>
          <ModalCloseButton />
          <EditBlockModal
            viewBlock={viewBlock}
            onClose={onClose}
            blockId={blockId}
            renderMeFn={renderMeFn}
          />
        </ModalContent>
      </Modal>

      {/* Save Changes Alert */}
      <AlertDialog
        motionPreset="slideInBottom"
        onClose={onClose1}
        isOpen={isOpen1}
        isCentered
      >
        <AlertDialogOverlay />

        <AlertDialogContent maxW={{ base: 'sm', md: '500px' }} w={{ base: '100%', md: 'auto' }}>
          <AlertDialogHeader>Save Changes</AlertDialogHeader>
          <AlertDialogCloseButton />
          <AlertDialogBody>
            Are you sure you want to make these changes.
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button
              onClick={() => {
                setBlockData({
                  result: prevBlockData,
                  isLoading: false,
                  error: false,
                });
                setAdjustBtnEdit(false);
                onClose1();
              }}
            >
              No
            </Button>
            <Button colorScheme="telegram" ml={3} onClick={updateBlockPosition}>
              Yes
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Create Block Modal */}
      <Modal isOpen={isOpen2} onClose={onClose2} size={{ base: "sm", md: "xl" }} isCentered>
        <ModalOverlay />
        <ModalContent mx={{ base: 4, md: 0 }} my={{ base: 8, md: "auto" }}>
          <ModalHeader fontSize={{ base: "lg", md: "xl" }}>Add New Block</ModalHeader>
          <ModalCloseButton />
          <ModalBody px={{ base: 4, md: 6 }}>
            <FormControl mb={4} isInvalid={!!createBlockErrors.identity}>
              <FormLabel fontSize={{ base: "sm", md: "md" }}>Identity <Text as="span" color="red.500">*</Text></FormLabel>
              <Input
                type="text"
                placeholder="Enter block identity"
                value={createBlockData.identity}
                onChange={(e) => {
                  setCreateBlockData({ ...createBlockData, identity: e.target.value });
                  if (createBlockErrors.identity) setCreateBlockErrors(errors => ({ ...errors, identity: undefined }));
                }}
                size={{ base: "md", md: "md" }}
              />
              {createBlockErrors.identity && (
                <Text color="red.500" fontSize="xs" mt={1}>{createBlockErrors.identity}</Text>
              )}
            </FormControl>
            
            <FormControl mb={4} isInvalid={!!createBlockErrors.title}>
              <FormLabel fontSize={{ base: "sm", md: "md" }}>Title <Text as="span" color="red.500">*</Text></FormLabel>
              <Input
                type="text"
                placeholder="Enter block title"
                value={createBlockData.title}
                onChange={(e) => {
                  setCreateBlockData({ ...createBlockData, title: e.target.value });
                  if (createBlockErrors.title) setCreateBlockErrors(errors => ({ ...errors, title: undefined }));
                }}
                size={{ base: "md", md: "md" }}
              />
              {createBlockErrors.title && (
                <Text color="red.500" fontSize="xs" mt={1}>{createBlockErrors.title}</Text>
              )}
            </FormControl>
            
            <FormControl mb={4} isInvalid={!!createBlockErrors.position}>
              <FormLabel fontSize={{ base: "sm", md: "md" }}>Position <Text as="span" color="red.500">*</Text></FormLabel>
              <NumberInput
                value={createBlockData.position}
                onChange={(value) => {
                  setCreateBlockData({ ...createBlockData, position: parseInt(value) || "" });
                  if (createBlockErrors.position) setCreateBlockErrors(errors => ({ ...errors, position: undefined }));
                }}
                size={{ base: "md", md: "md" }}
              >
                <NumberInputField placeholder="Enter position" />
              </NumberInput>
              {createBlockErrors.position && (
                <Text color="red.500" fontSize="xs" mt={1}>{createBlockErrors.position}</Text>
              )}
            </FormControl>
            
            <FormControl mb={4} isInvalid={!!createBlockErrors.max}>
              <FormLabel fontSize={{ base: "sm", md: "md" }}>Max <Text as="span" color="red.500">*</Text></FormLabel>
              <Select
                placeholder="Select max value"
                value={createBlockData.max}
                onChange={(e) => {
                  setCreateBlockData({...createBlockData, max: parseInt(e.target.value) || ""});
                  if (createBlockErrors.max) setCreateBlockErrors(errors => ({ ...errors, max: undefined }));
                }}
                size={{ base: "md", md: "md" }}
                width="100px"
                minW="100px"
                maxW="100px"
              >
                {[...Array(15)].map((_, i) => (
                  <option key={i + 1} value={i + 1}>{i + 1}</option>
                ))}
              </Select>
              {createBlockErrors.max && (
                <Text color="red.500" fontSize="xs" mt={1}>{createBlockErrors.max}</Text>
              )}
            </FormControl>
            
            <FormControl mb={4} isInvalid={!!createBlockErrors.collectionName}>
              <FormLabel fontSize={{ base: "sm", md: "md" }}>Collection Name <Text as="span" color="red.500">*</Text></FormLabel>
              <Select
                placeholder="Select collection type"
                value={createBlockData.collectionName}
                onChange={(e) => {
                  setCreateBlockData({...createBlockData, collectionName: e.target.value});
                  if (createBlockErrors.collectionName) setCreateBlockErrors(errors => ({ ...errors, collectionName: undefined }));
                }}
                size={{ base: "md", md: "md" }}
              >
                {availableCollectionOptions.map(opt => (
                  <option key={opt.value} value={opt.value}>{opt.label}</option>
                ))}
              </Select>
              {createBlockErrors.collectionName && (
                <Text color="red.500" fontSize="xs" mt={1}>{createBlockErrors.collectionName}</Text>
              )}
            </FormControl>
            
            <FormControl mb={4}>
              <FormLabel fontSize={{ base: "sm", md: "md" }}>Visibility</FormLabel>
              <Select
                value={createBlockData.visibility}
                onChange={(e) => setCreateBlockData({...createBlockData, visibility: e.target.value === "true"})}
                size={{ base: "md", md: "md" }}
              >
                <option value={true}>Visible</option>
                <option value={false}>Not Visible</option>
              </Select>
            </FormControl>
          </ModalBody>
          
          <ModalFooter px={{ base: 4, md: 6 }} flexDirection={{ base: "column", sm: "row" }} gap={{ base: 2, sm: 0 }}>
            <Button
              colorScheme="red"
              mr={{ base: 0, sm: 3 }}
              mb={{ base: 2, sm: 0 }}
              w={{ base: "full", sm: "auto" }}
              onClick={() => {
                onClose2();
                setCreateBlockData({
                  identity: "",
                  title: "",
                  visibility: true,
                  position: "",
                  max: "",
                  collectionName: ""
                });
              }}
            >
              Cancel
            </Button>
            <Button
              colorScheme="green"
              isLoading={isCreateLoading}
              onClick={createBlock}
              w={{ base: "full", sm: "auto" }}
            >
              Create Block
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Layout>
  );
};

export default BlocksCms;
