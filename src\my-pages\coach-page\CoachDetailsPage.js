import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import {
  Box,
  Flex,
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Tooltip,
  Text,
  Spinner,
  Badge,
  useToast,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Button,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  Tag,
  VStack,
  Heading,
  Image,
} from "@chakra-ui/react";
import { Link, useNavigate, useParams } from "react-router-dom";
import { IoArrowBackCircleOutline } from "react-icons/io5";
import BasicDetailsCoach from "./BasicDetailsCoach";
import ProfessionalDetails from "./ProfessionalDetails";
import KYCDetailsCoach from "./KYCDetailsCoach";
import axios from "axios";
import { FaAngleDown } from "react-icons/fa";
import { useSelector } from "react-redux";
import CoachCalendar from "../Calendar/Calendar";

const CoachCreation = () => {
  const [coachData, setCoachData] = useState({
    result: {},
    error: false,
    isLoading: false,
  });

  const [coachStatusChange, setCoachStatusChange] = useState({
    type: "",
    id: "",
  });

  const [coachAuthStatusChange, setCoachAuthStatusChange] = useState({
    type: "",
    id: "",
  });

  const [renderMe, setRenderMe] = useState(0);
  const userData = useSelector((state) => state.user);

  const [isOpen3, setIsOpen3] = useState(false);
  const onClose3 = () => setIsOpen3(false);
  const onOpen3 = () => setIsOpen3(true);

  const [isOpen4, setIsOpen4] = useState(false);
  const onClose4 = () => setIsOpen4(false);
  const onOpen4 = () => setIsOpen4(true);

  const navigate = useNavigate();
  const { id } = useParams();
  const toast = useToast();
  const token = sessionStorage.getItem("admintoken").split(" ")[1];

  // Get coach by id and pass data through props in component //

  const changeCoachStatus = () => {
    let data = JSON.stringify({
      status: coachStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/updateStatus/${coachStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setCoachStatusChange({ type: "", id: "" });
        onClose3();
        toast({
          title: "Coach status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setCoachStatusChange({ type: "", id: "" });
        onClose3();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const updateAuthStatus = () => {
    let data = JSON.stringify({
      authStatus: coachAuthStatusChange?.type,
    });

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/updateAuthStatus/${coachAuthStatusChange?.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      data: data,
    };

    axios
      .request(config)
      .then((response) => {
        setRenderMe((prev) => prev + 1);
        setCoachAuthStatusChange({ type: "", id: "" });
        onClose4();
        toast({
          title: "Coach auth status updated",
          status: "success",
          duration: 4000,
          position: "top",
          isClosable: true,
        });
      })
      .catch((error) => {
        console.log(error);
        setCoachAuthStatusChange({ type: "", id: "" });
        onClose4();
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  useEffect(() => {
    setCoachData({ result: {}, error: false, isLoading: true });
    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/coach/${id}`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };

    axios
      .request(config)
      .then((response) => {
        const coachResult = response.data;
        setCoachData({ result: coachResult, error: false, isLoading: false });
      })
      .catch((error) => {
        console.log(error);
        setCoachData({ result: {}, error: true, isLoading: false });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  }, [id, renderMe, navigate, toast, token]);

  return (
    <Box bgColor={"#f2f2f2"}>
      <Layout title="Coach Details">
        {/* Breadcrumb */}
        <Flex 
          justifyContent={"space-between"} 
          alignItems={"start"} 
          mb={3}
          flexDirection={{ base: "column", md: "row" }}
          gap={{ base: 2, md: 0 }}
        >
          <Flex justifyContent={"center"} alignItems={"center"} flexWrap="wrap">
            <Tooltip label="Back">
              <Button
                variant="ghost"
                size={{ base: "xs", md: "sm" }}
                leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
                onClick={() => navigate(-1)}
                _hover={{ bg: "gray.100" }}
                className="p-0"
              >
              </Button>
            </Tooltip>
            <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
              <BreadcrumbItem isCurrentPage>
                <BreadcrumbLink href="#">
                  Update Coach - 
                  {coachData?.result?.firstName && coachData?.result?.lastName
                    ? `${coachData.result.firstName} ${coachData.result.lastName}`
                    : id}
                </BreadcrumbLink>
              </BreadcrumbItem>
            </Breadcrumb>
          </Flex>
          {userData?.accessScopes?.coach?.includes("read") &&
            !userData?.accessScopes?.coach?.includes("write") && (
              <Flex>
                <Badge
                  colorScheme={
                    coachData?.result?.status === "active" ? "green" : "red"
                  }
                  mr={4}
                  variant="solid"
                  fontSize="0.9em"
                >
                  {coachData?.result?.status?.toUpperCase()}
                </Badge>
                <Badge
                  colorScheme={
                    coachData?.result?.authStatus === "authorized"
                      ? "green"
                      : "red"
                  }
                  variant="solid"
                  fontSize="0.9em"
                >
                  {coachData?.result?.authStatus?.toUpperCase()}
                </Badge>
              </Flex>
            )}

          {userData?.accessScopes?.coach?.includes("write") && (
            <Flex>
              <Menu>
                <Tooltip
                  label={
                    !coachData?.result?.kycDocuments?.documentImg?.length &&
                    "KYC details not available"
                  }
                >
                  <MenuButton
                    as={Button}
                    size={"sm"}
                    variant={"outline"}
                    isDisabled={
                      !coachData?.result?.kycDocuments?.documentImg?.length
                    }
                    py={2}
                    mr={3}
                    colorScheme={
                      coachData?.result?.status === "active" ? "green" : "red"
                    }
                    rightIcon={<FaAngleDown />}
                  >
                    {coachData?.result?.status?.toUpperCase()}
                  </MenuButton>
                </Tooltip>
                <MenuList>
                  <MenuItem
                    isDisabled={coachData?.result?.status === "active"}
                    onClick={() => {
                      setCoachStatusChange({
                        type: "active",
                        id: coachData?.result?._id,
                      });
                      onOpen3();
                    }}
                  >
                    Active
                  </MenuItem>
                  <MenuItem
                    isDisabled={coachData?.result?.status !== "active"}
                    onClick={() => {
                      setCoachStatusChange({
                        type: "inactive",
                        id: coachData?.result?._id,
                      });
                      onOpen3();
                    }}
                  >
                    Inactive
                  </MenuItem>
                </MenuList>
              </Menu>
              {coachData?.result?.authStatus === "authorized" ? (
                <Box>
                  <Tag
                    variant="solid"
                    size={"lg"}
                    colorScheme="green"
                    letterSpacing={"0.05rem"}
                  >
                    AUTHORIZED
                  </Tag>
                </Box>
              ) : (
                <Menu>
                  <MenuButton
                    as={Button}
                    size={"sm"}
                    variant={"outline"}
                    py={2}
                    colorScheme={
                      coachData?.result?.authStatus === "authorized"
                        ? "green"
                        : "red"
                    }
                    rightIcon={<FaAngleDown />}
                  >
                    {coachData?.result?.authStatus?.toUpperCase()}
                  </MenuButton>
                  <MenuList>
                    <MenuItem
                      isDisabled={
                        coachData?.result?.authStatus === "authorized"
                      }
                      onClick={() => {
                        setCoachAuthStatusChange({
                          type: "authorized",
                          id: coachData?.result?._id,
                        });
                        onOpen4();
                      }}
                    >
                      Authorized
                    </MenuItem>
                    {/* <MenuItem
                      isDisabled={
                        coachData?.result?.authStatus !== "authorized"
                      }
                      onClick={() => {
                        setCoachAuthStatusChange({
                          type: "unauthorized",
                          id: coachData?.result?._id,
                        });
                        onOpen4();
                      }}
                    >
                      Unauthorized
                    </MenuItem> */}
                  </MenuList>
                </Menu>
              )}
            </Flex>
          )}
        </Flex>
        {/* --- Tabs --- */}
        {coachData.isLoading && !coachData.error ? (
          <Flex my={20} justifyContent={"center"} alignItems={"center"}>
            <Spinner size={"xl"} />
          </Flex>
        ) : (
          <Flex justifyContent={"flex-start"} alignItems={"center"}>
            <Tabs colorScheme="telegram" size={"md"} w={"full"}>
              <TabList>
                <Tab>Calendar</Tab>
                <Tab>Basic Details</Tab>
                <Tab>Professional Details</Tab>
                <Tab>KYC Details</Tab>
              </TabList>

              <TabPanels>
                <TabPanel px={0}>
                  {!coachData.result?.refreshToken ? (
                    <Flex minH="50vh" align="center" justify="center">
                      <Box
                        maxW="md"
                        w="full"
                        bg="white"
                        boxShadow="lg"
                        rounded="lg"
                        p={8}
                        textAlign="center"
                      >
                        <VStack spacing={6}>
                          <Image
                            src="https://articles-images.sftcdn.net/wp-content/uploads/sites/3/2018/06/google-calendar-1024x576.jpg"
                            alt="Google Calendar"
                            h="20"
                            w="auto"
                            mx="auto"
                          />
                          
                          <Heading
                            as="h2"
                            size="lg"
                            fontWeight="bold"
                            color="gray.900"
                            lineHeight="tight"
                          >
                            Coach is not connected to google calender yet
                          </Heading>
                          
                          <Text color="gray.600" fontSize="sm">
                            Coach needs to link their Google Calendar to view and manage their schedule.
                          </Text>
                        </VStack>
                      </Box>
                    </Flex>
                  ) : (
                    <CoachCalendar coachData={coachData.result} />
                  )}
                </TabPanel>
                <TabPanel px={0}>
                  <BasicDetailsCoach coachData={coachData.result} />
                </TabPanel>
                <TabPanel px={0}>
                  <ProfessionalDetails coachData={coachData.result} />
                </TabPanel>
                <TabPanel px={0}>
                  <KYCDetailsCoach coachData={coachData.result} />
                </TabPanel>
              </TabPanels>
            </Tabs>
          </Flex>
        )}
        {/* Status - alert */}
        <AlertDialog isOpen={isOpen3} onClose={onClose3} isCentered>
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Update Coach Status
              </AlertDialogHeader>

              <AlertDialogBody>
                When updating the coach status, an automatic email will be
                triggered to notify coach the relevant changes
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  colorScheme="red"
                  onClick={() => {
                    onClose3();
                    setCoachStatusChange({ type: "", id: "" });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  colorScheme="green"
                  onClick={() => {
                    changeCoachStatus();
                  }}
                  ml={3}
                >
                  Save Changes
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
        {/* Auth Status - alert */}
        <AlertDialog isOpen={isOpen4} onClose={onClose4} isCentered>
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Update Coach Auth Status
              </AlertDialogHeader>

              <AlertDialogBody>
                When updating the coach auth status, an automatic email will be
                triggered to notify coach the relevant changes
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  colorScheme="red"
                  onClick={() => {
                    onClose4();
                    setCoachAuthStatusChange({ type: "", id: "" });
                  }}
                >
                  Cancel
                </Button>
                <Button
                  colorScheme="green"
                  onClick={() => {
                    updateAuthStatus();
                  }}
                  ml={3}
                >
                  Save Changes
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Layout>
    </Box>
  );
};

export default CoachCreation;
