import React, { useEffect, useState } from "react";
import Layout from "../../layout/default";
import { Link, useLocation, useNavigate } from "react-router-dom";
import ReactPaginate from "react-paginate";
import {
  BreadcrumbLink,
  Breadcrumb,
  BreadcrumbItem,
  Text,
  Flex,
  TableContainer,
  Table,
  Tbody,
  Tr,
  Td,
  Thead,
  Th,
  Spinner,
  Tooltip,
  useToast,
  Box,
  InputGroup,
  Input,
  InputRightAddon,
  Select,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverArrow,
  PopoverCloseButton,
  PopoverBody,
  Button,
  PopoverHeader,
  FormControl,
  FormLabel,
  Badge,
  SimpleGrid,
  Card,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  VStack,
  Icon,
} from "@chakra-ui/react";
import axios from "axios";
import { IoIosClose, IoMdSearch } from "react-icons/io";
import { IoFilter } from "react-icons/io5";
import { useMediaQuery } from "@chakra-ui/react";
import BookingListMobileView from "./BookingdetailsMobile";
import { FaRupeeSign, FaCheckCircle, FaClock } from "react-icons/fa";
import { IoArrowBackCircleOutline } from "react-icons/io5";
const Booking = () => {
  const [bookingData, setBookingData] = useState({
    result: [],
    isLoading: false,
    error: false,
    notFound: false,
  });
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedStartDate, setSelectedStartDate] = useState("");
  const [selectedEndDate, setSelectedEndDate] = useState("");
  const [selectedClassType, setSelectedClassType] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [searchCourseName, setSearchCourseName] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [startDateError, setStartDateError] = useState(false);
  const [endDateError, setEndDateError] = useState(false);
  const [paymentStats, setPaymentStats] = useState({
    totalPayment: 0,
    paymentReceived: 0,
    paymentPending: 0
  });

  const token = sessionStorage?.getItem("admintoken")?.split(" ")[1];
  const toast = useToast();
  const location = useLocation();

  const [filters, setFilters] = useState({
    startDate: "",
    endDate: "",
  });

  const navigate = useNavigate();

  useEffect(() => {
    const newParams = new URLSearchParams();
    Object.keys(filters).forEach((key) => {
      if (filters[key]) {
        newParams.append(key, filters[key]);
      }
    });
    navigate(`?${newParams.toString()}`, { replace: true });
  }, [filters, navigate]);

  const handleFilterChange = (event) => {
    const { name, value } = event.target;
    if (name === "startDate") {
      setStartDateError(false);
    }
    if (name === "endDate") {
      setEndDateError(false);
    }
    setFilters((prevFilters) => ({
      ...prevFilters,
      [name]: value,
    }));
  };

  const getBookingData = (
    startDate,
    endDate,
    courseType,
    status,
    courseName
  ) => {
    setBookingData({
      result: [],
      isLoading: true,
      error: false,
      notFound: false,
    });
    let queryString = "?";

    if (courseName) {
      queryString += `courseName=${courseName}`;
    } else {
      queryString = `?page=${currentPage}`;
    }

    if (startDate) {
      queryString += `${queryString ? "&" : ""}startDate=${startDate}`;
    }

    if (endDate) {
      queryString += `${queryString ? "&" : ""}endDate=${endDate}`;
    }

    if (courseType) {
      queryString += `${queryString ? "&" : ""}courseType=${courseType}`;
    }

    if (status) {
      queryString += `${queryString ? "&" : ""}status=${status}`;
    }

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `${process.env.REACT_APP_BASE_URL}/api/booking/${
        queryString ? `${queryString}` : ""
      }`,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    axios
      .request(config)
      .then((response) => {
        setBookingData({
          result: response.data,
          isLoading: false,
          error: false,
          notFound: response.data.message === "No bookings found",
        });
        setTotalPages(Math.ceil(response.data.totalResults / 25));
      })
      .catch((error) => {
        console.log(error);
        setBookingData({
          result: [],
          isLoading: false,
          error: true,
          notFound: false,
        });
        if (error.response.status === 403) {
          toast({
            title: "You don't have an access to perform this action",
            status: "warning",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        } else {
          toast({
            title: "Something went wrong please try again later",
            status: "error",
            duration: 4000,
            position: "top",
            isClosable: true,
          });
        }
      });
  };

  const handlePageChange = ({ selected }) => {
    setCurrentPage(selected + 1);
  };

  const formatDate = (date) => {
    let originalDate = new Date(date);

    let day = ("0" + originalDate.getDate()).slice(-2);
    let month = ("0" + (originalDate.getMonth() + 1)).slice(-2);
    let year = originalDate.getFullYear();

    let desiredDateFormat = day + "-" + month + "-" + year;

    return desiredDateFormat;
  };

  useEffect(() => {
    getBookingData(
      startDate,
      endDate,
      selectedClassType,
      selectedStatus,
      searchCourseName
    );
  }, [
    currentPage,
    selectedClassType,
    selectedStatus,
    searchCourseName,
    startDate,
    endDate,
  ]);

  const getPaymentStats = async () => {
    try {
      let queryString = `?page=1`;
      
      if (startDate) {
        queryString += `&startDate=${startDate}`;
      }
      
      if (endDate) {
        queryString += `&endDate=${endDate}`;
      }
      
      const response = await axios.get(
        `${process.env.REACT_APP_BASE_URL}/api/booking/cards${queryString}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      setPaymentStats(response.data.cards);
    } catch (error) {
      console.error("Error fetching payment stats:", error);
      setPaymentStats({
        totalPayment: 0,
        paymentReceived: 0,
        paymentPending: 0
      });
    }
  };

  useEffect(() => {
    getPaymentStats();
  }, [startDate, endDate]);

  return (
    <Layout title="Booking">
      {isMobile ? (
        <BookingListMobileView
          data={bookingData?.result?.data || []}
          isLoading={bookingData?.isLoading}
          searchCourseName={searchCourseName}
          setSearchCourseName={setSearchCourseName}
          selectedClassType={selectedClassType}
          setSelectedClassType={setSelectedClassType}
          selectedStatus={selectedStatus}
          setSelectedStatus={setSelectedStatus}
          startDate={startDate}
          endDate={endDate}
          setStartDate={setStartDate}
          setEndDate={setEndDate}
          filters={filters}
          setFilters={setFilters}
          formatDate={formatDate}
          totalPages={totalPages}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          handlePageChange={handlePageChange}
          showSearch={showSearch}
          setShowSearch={setShowSearch}
          paymentStats={paymentStats} // Pass payment stats to mobile view
        />
      ) : (
        <>
          {/* Breadcrumb */}
          <Box mb={6}>
            <Box px={{ base: 0, md: 1 }} py={2}>
              <Flex alignItems="center" gap={0} mb={3}>
                <Button
                  variant="ghost"
                  size={{ base: "xs", md: "sm" }}
                  leftIcon={<IoArrowBackCircleOutline fontSize={"24px"} />}
                  onClick={() => navigate(-1)}
                  _hover={{ bg: "gray.100" }}
                  color="gray.700"
                  fontWeight="bold"
                  className="p-0"
                >
                </Button>
                <Breadcrumb fontWeight="medium" fontSize="sm" pb={0.5}>
                  <BreadcrumbItem isCurrentPage>
                    <BreadcrumbLink href="#">Booking</BreadcrumbLink>
                  </BreadcrumbItem>
                </Breadcrumb>
              </Flex>
            </Box>
          </Box>
          {/* Payment Stats Cards - Made smaller */}
          <SimpleGrid columns={{ base: 1, md: 3 }} spacing={4} mb={6}>
            <Card bg="white" shadow="md" borderRadius="md" overflow="hidden" size="sm">
              <CardBody p={4}>
                <Flex align="center" justify="space-between">
                  <Stat>
                    <StatLabel color="gray.600" fontSize="xs" fontWeight="medium">
                      Total Payment
                    </StatLabel>
                    <StatNumber fontSize="lg" fontWeight="bold" color="blue.600">
                      ₹{paymentStats.totalPayment?.toFixed(2) || "0.00"}
                    </StatNumber>
                    <StatHelpText color="gray.500" fontSize="xs" mb={0}>
                      Overall revenue
                    </StatHelpText>
                  </Stat>
                  <Box
                    p={2}
                    bg="blue.50"
                    borderRadius="md"
                    color="blue.500"
                  >
                    <Icon as={FaRupeeSign} boxSize={4} />
                  </Box>
                </Flex>
              </CardBody>
            </Card>

            <Card bg="white" shadow="md" borderRadius="md" overflow="hidden" size="sm">
              <CardBody p={4}>
                <Flex align="center" justify="space-between">
                  <Stat>
                    <StatLabel color="gray.600" fontSize="xs" fontWeight="medium">
                      Payment Received
                    </StatLabel>
                    <StatNumber fontSize="lg" fontWeight="bold" color="green.600">
                      ₹{paymentStats.paymentReceived?.toFixed(2) || "0.00"}
                    </StatNumber>
                    <StatHelpText color="gray.500" fontSize="xs" mb={0}>
                      Successfully collected
                    </StatHelpText>
                  </Stat>
                  <Box
                    p={2}
                    bg="green.50"
                    borderRadius="md"
                    color="green.500"
                  >
                    <Icon as={FaCheckCircle} boxSize={4} />
                  </Box>
                </Flex>
              </CardBody>
            </Card>

            <Card bg="white" shadow="md" borderRadius="md" overflow="hidden" size="sm">
              <CardBody p={4}>
                <Flex align="center" justify="space-between">
                  <Stat>
                    <StatLabel color="gray.600" fontSize="xs" fontWeight="medium">
                      Payment Pending
                    </StatLabel>
                    <StatNumber fontSize="lg" fontWeight="bold" color="orange.600">
                      ₹{paymentStats.paymentPending?.toFixed(2) || "0.00"}
                    </StatNumber>
                    <StatHelpText color="gray.500" fontSize="xs" mb={0}>
                      Awaiting payment
                    </StatHelpText>
                  </Stat>
                  <Box
                    p={2}
                    bg="orange.50"
                    borderRadius="md"
                    color="orange.500"
                  >
                    <Icon as={FaClock} boxSize={4} />
                  </Box>
                </Flex>
              </CardBody>
            </Card>
          </SimpleGrid>

          {/* Search and Filters */}
          <Flex
            direction={{ base: "column", lg: "row" }}
            gap={4}
            mb={6}
            align={{ base: "stretch", lg: "center" }}
            justify="space-between"
          >
            {/* Left - Search */}
            <Box flex={{ base: "1", lg: "0 0 40%" }}>
              {showSearch ? (
                <InputGroup size="md">
                  <Input
                    pr="4.5rem"
                    type="text"
                    placeholder="Search by training schedule name"
                    borderColor="gray.300"
                    bg="white"
                    _focus={{ borderColor: "blue.400", boxShadow: "0 0 0 1px #3182ce" }}
                    onChange={(e) => {
                      if (e.target.value.length >= 3) {
                        setTimeout(() => {
                          setSearchCourseName(e.target.value);
                        }, 500);
                      }
                      if (e.target.value.length === 0) {
                        setSearchCourseName("");
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        if (e.target.value.length >= 3) {
                          setSearchCourseName(e.target.value);
                        }
                      }
                    }}
                  />
                  <InputRightAddon
                    bgColor="gray.100"
                    border="1px"
                    borderColor="gray.300"
                    onClick={() => {
                      setShowSearch(false);
                      setSearchCourseName("");
                    }}
                    cursor="pointer"
                    _hover={{ bg: "gray.200" }}
                  >
                    <IoIosClose fontSize="20px" />
                  </InputRightAddon>
                </InputGroup>
              ) : (
                <Button
                  leftIcon={<IoMdSearch />}
                  rightIcon={<IoFilter />}
                  variant="outline"
                  borderColor="gray.300"
                  color="gray.600"
                  bg="white"
                  _hover={{ bg: "gray.50" }}
                  onClick={() => setShowSearch(true)}
                  size="md"
                  w={{ base: "full", lg: "auto" }}
                >
                  Search & Filter
                </Button>
              )}
            </Box>

            {/* Right - Filters */}
            <Flex
              gap={3}
              flex={{ base: "1", lg: "0 0 55%" }}
              direction={{ base: "column", sm: "row" }}
              align="stretch"
            >
              {/* Date Range Filter */}
              <Box flex="1" position="relative">
                <Popover isOpen={isOpen}>
                  <PopoverTrigger>
                    <Button
                      variant="outline"
                      borderColor="gray.300"
                      color="gray.700"
                      bg={startDate && endDate ? "blue.50" : "white"}
                      _hover={{ bg: startDate && endDate ? "blue.100" : "gray.50" }}
                      onClick={() => {
                        setIsOpen(true);
                        setStartDateError(false);
                        setEndDateError(false);
                      }}
                      size="md"
                      w="full"
                      justifyContent="center"
                    >
                      Date Range
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent border="1px" borderColor="gray.300" shadow="lg">
                    <PopoverArrow />
                    <PopoverCloseButton
                      mt={1}
                      onClick={() => {
                        setIsOpen(false);
                        setStartDateError(false);
                        setEndDateError(false);
                      }}
                    />
                    <PopoverHeader fontWeight="semibold" bg="gray.50">
                      Select Date Range
                    </PopoverHeader>
                    <PopoverBody bg="white">
                      <VStack spacing={3}>
                        <FormControl isInvalid={startDateError}>
                          <FormLabel fontSize="sm">Start Date</FormLabel>
                          <InputGroup size="sm">
                            <Input
                              type="date"
                              max={
                                selectedEndDate ||
                                new Date().toISOString().split("T")[0]
                              }
                              value={filters.startDate}
                              name="startDate"
                              onChange={handleFilterChange}
                            />
                            <InputRightAddon
                              bgColor="gray.100"
                              border="1px"
                              borderColor="gray.300"
                              cursor="pointer"
                              onClick={() => {
                                setStartDate("");
                                setFilters((prevFilters) => ({
                                  ...prevFilters,
                                  startDate: "",
                                }));
                              }}
                            >
                              <IoIosClose fontSize="16px" />
                            </InputRightAddon>
                          </InputGroup>
                          {startDateError && (
                            <Text color="red.500" fontSize="xs">
                              Enter start date
                            </Text>
                          )}
                        </FormControl>
                        
                        <FormControl isInvalid={endDateError}>
                          <FormLabel fontSize="sm">End Date</FormLabel>
                          <InputGroup size="sm">
                            <Input
                              type="date"
                              min={selectedStartDate}
                              max={new Date().toISOString().split("T")[0]}
                              value={filters.endDate}
                              name="endDate"
                              onChange={handleFilterChange}
                            />
                            <InputRightAddon
                              bgColor="gray.100"
                              border="1px"
                              borderColor="gray.300"
                              cursor="pointer"
                              onClick={() => {
                                setEndDate("");
                                setFilters((prevFilters) => ({
                                  ...prevFilters,
                                  endDate: "",
                                }));
                              }}
                            >
                              <IoIosClose fontSize="16px" />
                            </InputRightAddon>
                          </InputGroup>
                          {endDateError && (
                            <Text color="red.500" fontSize="xs">
                              Enter end date
                            </Text>
                          )}
                        </FormControl>
                        
                        <Button
                          colorScheme="blue"
                          size="sm"
                          w="full"
                          isDisabled={!filters.startDate && !filters.endDate}
                          onClick={() => {
                            if (!filters.startDate) {
                              setStartDateError(true);
                              return;
                            }
                            if (!filters.endDate) {
                              setEndDateError(true);
                            }
                            if (filters.startDate && filters.endDate) {
                              setIsOpen(false);
                              setStartDate(filters.startDate);
                              setEndDate(filters.endDate);
                            }
                          }}
                        >
                          Apply
                        </Button>
                      </VStack>
                    </PopoverBody>
                  </PopoverContent>
                </Popover>
                
                {/* Remove filter badge */}
                {(startDate || endDate) && (
                  <Tooltip label="Remove filter">
                    <Box
                      position="absolute"
                      top={-1}
                      right={-1}
                      bgColor="red.500"
                      borderRadius="full"
                      p={1}
                      cursor="pointer"
                      onClick={() => {
                        setFilters({
                          startDate: "",
                          endDate: "",
                        });
                        setStartDate("");
                        setEndDate("");
                      }}
                    >
                      <IoIosClose fontSize="10px" color="white" />
                    </Box>
                  </Tooltip>
                )}
              </Box>

              {/* Class Type Filter */}
              <Box flex="1">
                <Select
                  placeholder="Class Type"
                  borderColor="gray.300"
                  bg="white"
                  _focus={{ borderColor: "blue.400" }}
                  value={selectedClassType}
                  onChange={(e) => {
                    setSelectedClassType(e.target.value);
                    setCurrentPage(1);
                  }}
                  size="md"
                >
                  <option value="">All Types</option>
                  <option value="class">Class</option>
                  <option value="course">Course</option>
                  <option value="event">Event</option>
                </Select>
              </Box>

              {/* Status Filter */}
              <Box flex="1">
                <Select
                  placeholder="Status"
                  borderColor="gray.300"
                  bg="white"
                  _focus={{ borderColor: "blue.400" }}
                  value={selectedStatus}
                  onChange={(e) => {
                    setSelectedStatus(e.target.value);
                    setCurrentPage(1);
                  }}
                  size="md"
                >
                  <option value="">All Status</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="pending">Pending</option>
                  <option value="cancelled">Cancelled</option>
                </Select>
              </Box>
            </Flex>
          </Flex>
          {/* Added/Selected Course List */}
          {!bookingData?.isLoading && bookingData?.error ? (
            <Flex
              justifyContent={"center"}
              alignItems={"center"}
              w={"full"}
              my={10}
            >
              <Text color={"red.500"}>
                Something went wrong please try again later...
              </Text>
            </Flex>
          ) : (
            <>
              <TableContainer
                mt={6}
                height={`${window.innerHeight - 235}px`}
                overflowY={"scroll"}
              >
                <Table variant="simple">
                  <Thead bgColor={"#c1eaee"} position={"sticky"} top={"0px"}>
                    <Tr bgColor={"#E2DFDF"}>
                      <Th>Booking Id</Th>
                      <Th>Course</Th>
                      <Th>Customer</Th>
                      <Th>Type</Th>
                      <Th>Amount</Th>
                      <Th>Date</Th>
                      <Th>Status</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {bookingData?.isLoading && !bookingData?.error ? (
                      <Tr>
                        {/* <Td></Td> */}
                        <Td></Td>
                        <Td></Td>
                        <Td></Td>
                        <Td
                          display={"flex"}
                          justifyContent={"center"}
                          alignItems={"center"}
                        >
                          <Spinner />
                        </Td>
                        <Td></Td>
                        <Td></Td>
                        <Td></Td>
                      </Tr>
                    ) : !bookingData?.notFound ? (
                      bookingData?.result?.data?.map((bookData, inx) => {
                        return (
                          <Tr
                            cursor={"pointer"}
                            key={bookData._id}
                            onClick={() =>
                              navigate(`/Booking/details/${bookData._id}`)
                            }
                          >
                            <Td fontSize={"14px"}>
                              {bookData?.bookingId}
                            </Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              fontSize={"14px"}
                            >
                              {bookData?.courseName || "n/a"}
                            </Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              fontSize={"14px"}
                            >
                              {bookData?.playerName || "n/a"}
                            </Td>
                            <Td fontSize={"14px"}>
                              {bookData?.courseType?.charAt(0).toUpperCase() +
                                bookData?.courseType?.slice(1) || "n/a"}{" "}
                            </Td>
                            <Td fontSize={"14px"}>
                              &#8377;{bookData?.pricePaid?.toFixed(2) || "n/a"}{" "}
                            </Td>
                            <Td fontSize={"14px"}>
                              {formatDate(bookData?.createdAt) || "n/a"}{" "}
                            </Td>
                            <Td
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                              }}
                              px={4}
                              fontSize={"14px"}
                            >
                              <Badge
                                colorScheme={
                                  bookData?.status?.toLowerCase() === "active"
                                    ? "green"
                                    : "red"
                                }
                              >
                                {bookData?.status}
                              </Badge>
                            </Td>
                          </Tr>
                        );
                      })
                    ) : (
                      <Tr>
                        <Td></Td>
                        <Td></Td>
                        <Td></Td>
                        <Td
                          display={"flex"}
                          justifyContent={"center"}
                          alignItems={"center"}
                        >
                          <Text color={"green.500"} fontWeight={"semibold"}>
                            No result found
                          </Text>
                        </Td>
                        <Td></Td>
                        <Td></Td>
                        <Td></Td>
                      </Tr>
                    )}
                  </Tbody>
                </Table>
              </TableContainer>
              {/* Pagination */}
              {!bookingData?.notFound && (
                <Flex
                  justifyContent="center"
                  alignItems="center"
                  flexDirection={"row"}
                  w={"100%"}
                  mt={5}
                >
                  <ReactPaginate
                    previousLabel="Previous"
                    nextLabel="Next"
                    breakLabel="..."
                    pageCount={totalPages}
                    marginPagesDisplayed={2}
                    pageRangeDisplayed={5}
                    onPageChange={handlePageChange}
                    containerClassName="pagination"
                    activeClassName="active"
                    forcePage={currentPage - 1}
                  />
                </Flex>
              )}
            </>
          )}
        </>
      )}
    </Layout>
  );
};

export default Booking;
